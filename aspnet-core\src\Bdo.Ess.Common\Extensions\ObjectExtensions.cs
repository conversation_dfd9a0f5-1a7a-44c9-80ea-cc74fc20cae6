using System;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Bdo.Ess.Common.Helpers;
using System.IO;
using System.Reflection;

namespace Bdo.Ess.Common.Extensions
{
    public static class ObjectExtensions
    {
        public static string ConvertObjectToCompressedJson<T>(this T source, Newtonsoft.Json.Converters.StringEnumConverter optionStringEnumConverter = null, int maxDepth = 0, DefaultSerializationBinder binder = null)
        {
            string result = string.Empty;
            try
            {
                var settings = new JsonSerializerSettings { TypeNameHandling = TypeNameHandling.All, ReferenceLoopHandling = ReferenceLoopHandling.Ignore };
                if (binder != null)
                {
                    settings.SerializationBinder = binder;
                }

                if (optionStringEnumConverter != null)
                {
                    settings.Converters = new Newtonsoft.Json.Converters.StringEnumConverter[] { optionStringEnumConverter };
                }

                if (maxDepth == 0)
                {
                    result = JsonConvert.SerializeObject(source, settings)?.Compress();
                }
                else
                {
                    result = SerializeObject(source, maxDepth)?.Compress();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError("ConvertToJson Error:");
                System.Diagnostics.Trace.TraceError(ex.ToString());
            }
            return result;
        }

        public static T ConvertCompressedJsonToObject<T>(this string source, DefaultSerializationBinder binder = null)
        {
            try
            {
                var settings = new JsonSerializerSettings { TypeNameHandling = TypeNameHandling.Auto, ReferenceLoopHandling = ReferenceLoopHandling.Ignore, MissingMemberHandling = MissingMemberHandling.Ignore };
                if (binder != null)
                {
                    settings.SerializationBinder = binder;
                }

                // if string is Json, do not decompress
                if (IsJson(source))
                {
                    return source.ConvertJsonToObject<T>(binder);
                }

                return JsonConvert.DeserializeObject<T>(source?.Decompress(), settings);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError("ConvertToJson Error:");
                System.Diagnostics.Trace.TraceError(ex.ToString());
            }
            return default(T);
        }


        public static string SerializeObject(object obj, int maxDepth)
        {
            using (var strWriter = new StringWriter())
            {
                using (var jsonWriter = new CustomJsonTextWriter(strWriter))
                {
                    Func<bool> include = () => jsonWriter.CurrentDepth <= maxDepth;
                    var resolver = new CustomContractResolver(include);
                    var serializer = new JsonSerializer { ContractResolver = resolver };
                    serializer.Serialize(jsonWriter, obj);
                }
                return strWriter.ToString();
            }
        }


        public static T ConvertJsonToObject<T>(this string source, DefaultSerializationBinder binder = null)
        {
            try
            {
                var settings = new JsonSerializerSettings { TypeNameHandling = TypeNameHandling.Auto, ReferenceLoopHandling = ReferenceLoopHandling.Ignore };
                if (binder != null)
                {
                    settings.SerializationBinder = binder;
                }
                return JsonConvert.DeserializeObject<T>(source, settings);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError("ConvertToJson Error:");
                System.Diagnostics.Trace.TraceError(ex.ToString());
            }
            return default(T);
        }

        private static bool IsJson(string source)
        {
            return string.IsNullOrEmpty(source) || source.StartsWith("{");
        }
    }



    public class CustomContractResolver : DefaultContractResolver
    {
        private readonly Func<bool> _includeProperty;

        public CustomContractResolver(Func<bool> includeProperty)
        {
            _includeProperty = includeProperty;
        }

        protected override JsonProperty CreateProperty(
            MemberInfo member, MemberSerialization memberSerialization)
        {
            var property = base.CreateProperty(member, memberSerialization);
            var shouldSerialize = property.ShouldSerialize;
            property.ShouldSerialize = obj => _includeProperty() &&
                                              (shouldSerialize == null ||
                                               shouldSerialize(obj));
            return property;
        }
    }

    public class CustomJsonTextWriter : JsonTextWriter
    {
        public CustomJsonTextWriter(TextWriter textWriter) : base(textWriter) { }

        public int CurrentDepth { get; private set; }

        public override void WriteStartObject()
        {
            CurrentDepth++;
            base.WriteStartObject();
        }

        public override void WriteEndObject()
        {
            CurrentDepth--;
            base.WriteEndObject();
        }
    }
}
