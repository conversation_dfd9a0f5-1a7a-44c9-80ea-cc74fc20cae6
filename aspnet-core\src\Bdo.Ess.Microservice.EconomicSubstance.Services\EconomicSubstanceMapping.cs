using AutoMapper;
using Bdo.Ess.Common.EconomicSubstance;
using Bdo.Ess.Common.Enums;
using Bdo.Ess.Common.Helpers;
using Bdo.Ess.Dtos.Audit.EconomicSubstance;
using Bdo.Ess.Dtos.EconomicSubstance;
using Bdo.Ess.Dtos.LookUp;
using Bdo.Ess.Dtos.MasterData;
using Bdo.Ess.Microservice.Core.EntityFramework.Ctsp.Objects;
using Bdo.Ess.Microservice.EconomicSubstance.EntityFramework.Objects;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
namespace Bdo.Ess.Microservice.EconomicSubstance.Services
{



    public static class EconomicSubstanceMapping
    {
        const string EvidenceNonResidency = "EvidenceNonResidency";
        const string EvidenceProvisionalTreatment = "EvidenceProvisionalTreatment";
        const string SupportingDocument = "SupportingDocument";


        const string InformationRequestedDocument = "InformationRequestedDocument";
        const string ProvisionalTreatmentDocument = "ProvisionalTreatmentDocument";

        const string OtherCIGADocuments = "OtherCIGADocuments";
        const string HighRiskDocuments = "HighRiskDocuments";
        const string TangibleAssetIncomeDocuments = "TangibleAssetIncomeDocuments";
        const string TangibleAssetEmployeeResponsibilityDocuments = "TangibleAssetEmployeeResponsibilityDocuments";
        const string HistoryofStrategicDecisionsInBVIDocuments = "HistoryofStrategicDecisionsInBVIDocuments";
        const string HistoryofTradingActivityIncomeDocuments = "HistoryofTradingActivityIncomeDocuments";
        const string IPAssetsInBVIDocuments = "IPAssetsInBVIDocuments";
        const string IPAssetsEmployeeResponsibilityDocuments = "IPAssetsEmployeeResponsibilityDocuments";
        const string ConcreteEvidenceDecisionInBVIDocuments = "ConcreteEvidenceDecisionInBVIDocuments";

        const string ServiceProvidersType = "ServiceProviders";
        const string ManagementDetailsType = "ManagementDetails";
        const string AttendMeetingDetailsType = "AttendMeetingDetails";



        const string EmployeeQualificationDetailsType = "EmployeeQualificationDetails";

        private static Dictionary<Guid, CountryDto> _countryDict;
        private static Dictionary<Guid, CIGALookUpDto> _cigaDict;
        private static Dictionary<Guid, CurrencyDto> _currencyDict;
        private static CorporateEntityDto _corporateEntity;
        public static ILogger Logger { get; set; }

        public static void LogException(Exception ex)
        {
            Logger?.LogError(ex, "An error occurred.");
        }
        private static IMapper Mapper = new MapperConfiguration(cfg =>
        {

            cfg.CreateMap<Documents, DocumentsDto>();
            cfg.CreateMap<EconomicSubstanceDocuments, EvidenceNonResidencyDocumentsDto>();
            cfg.CreateMap<EconomicSubstanceDocuments, EvidenceProvisionalTreatmentDocumentsDto>();
            cfg.CreateMap<EconomicSubstanceDocuments, SupportingDocumentDto>();
            cfg.CreateMap<EconomicSubstanceDocuments, ProvisionalTreatmentDocumentDto>();
            cfg.CreateMap<EconomicSubstanceInformationRequiredDocumentsDto, EconomicSubstanceInformationRequiredDocuments>();

            cfg.CreateMap<EvidenceNonResidencyDocumentsDto, EconomicSubstanceDocuments>();
            cfg.CreateMap<EvidenceProvisionalTreatmentDocumentsDto, EconomicSubstanceDocuments>();
            cfg.CreateMap<SupportingDocumentDto, EconomicSubstanceDocuments>();
            cfg.CreateMap<ProvisionalTreatmentDocumentDto, EconomicSubstanceDocuments>();
            cfg.CreateMap<EconomicSubstanceInformationRequiredDocuments, EconomicSubstanceInformationRequiredDocumentsDto>();

            cfg.CreateMap<RelevantActivitiesDocuments, OtherCIGADocumentsDto>();
            cfg.CreateMap<RelevantActivitiesDocuments, HighRiskDocumentsDto>();
            cfg.CreateMap<RelevantActivitiesDocuments, GenericRelevantActivityDocumentDto>();
            cfg.CreateMap<RelevantActivitiesDocuments, DocumentAuditDto>();

            cfg.CreateMap<EconomicSubstanceDocuments, DocumentAuditDto>();

            cfg.CreateMap<CIGARelevantActivityDto, CIGARelevantActivity>();
            cfg.CreateMap<CIGARelevantActivity, CIGARelevantActivityDto>();

            cfg.CreateMap<CIGARelevantActivity, EconomicSubstanceCIGAAuditDto>();

            cfg.CreateMap<AddressDto, Address>();
            cfg.CreateMap<Address, AddressDto>();
            cfg.CreateMap<Address, AddressAuditDto>();
            cfg.CreateMap<ServiceProvidersDto, ServiceProvideMetaData>();
            cfg.CreateMap<PersonDetailsDto, PersonalDetailMetaData>();

            cfg.CreateMap<AttendMeetingDetailsDto, AttendMeetingDetailsMetaData>();
            cfg.CreateMap<EmployeeQualificationDetailsDto, EmployeeQualificationDetailsMetaData>();


            cfg.CreateMap<EsInformationRequestDocumentCaDto, EsInformationRequestDocumentCa>()
            .ReverseMap();

            cfg.CreateMap<EsEntityDetailDto, EsEntityDetail>();

            cfg.CreateMap<EsEntityAdditionalDetailDto, EsEntityAdditionalDetail>()
            .ForMember(dest => dest.EntityType, opt => opt.MapFrom(src => src.EntityType.ToString()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.IsNew ? Guid.Empty : src.Id));
           

        }).CreateMapper();



        public static EconomicSubstanceDeclarationDto FixDateTime(this EconomicSubstanceDeclarationDto input)
        {
            if (!string.IsNullOrEmpty(input.FiscalStartDateString))
                input.FiscalStartDate = input.FiscalStartDateString.ConvertToDateTime();
            if (!string.IsNullOrEmpty(input.FiscalEndDateString))
                input.FiscalEndDate = input.FiscalEndDateString.ConvertToDateTime();

            if (input.RelevantActivities != null)
            {
                var result = input.RelevantActivities.Where(x => x.IsChecked && !x.IsDeleted && x.ReleventActivityValue != RelevantActivity.None).ToList();

                result.ForEach(x =>
                {
                    if (!string.IsNullOrEmpty(x.StartDateString)) x.StartDate = x.StartDateString.ConvertToDateTime();
                    if (!string.IsNullOrEmpty(x.EndDateString)) x.EndDate = x.EndDateString.ConvertToDateTime();

                }
                );
            }
            return input;
        }


        public static void InitializeData(Dictionary<Guid, CountryDto> countryDict, Dictionary<Guid, CIGALookUpDto> cigaDict,
            Dictionary<Guid, CurrencyDto> currencyDict, CorporateEntityDto corporateEntity)
        {
            _countryDict = countryDict;
            _cigaDict = cigaDict;
            _corporateEntity = corporateEntity;
            _currencyDict = currencyDict;
        }

        static List<EconomicSubstanceDocuments> GetList<T>(this List<T> docList, string docType)
        {
            List<EconomicSubstanceDocuments> Items = new List<EconomicSubstanceDocuments>();

            Guid outGuid = Guid.Empty;
            foreach (var item in docList)
            {
                var type = item.GetType();
                var id = type.GetProperty("Id").GetValue(item, null);
                var docid = type.GetProperty("DocumentsId").GetValue(item);

                var docItem = new EconomicSubstanceDocuments();

                docItem.Id = id == null ? outGuid : (Guid)id;
                docItem.ContetntType = type.GetProperty("ContetntType").GetValue(item, null) as string;
                docItem.FileName = type.GetProperty("FileName").GetValue(item, null) as string;
                docItem.IsDeleted = (bool)type.GetProperty("IsDeleted").GetValue(item, null);
                docItem.DocumentCategory = docType;
                docItem.DocumentsId = docid == null ? outGuid : (Guid)docid;
                Items.Add(docItem);
            }
            return Items;
        }

        static List<RelevantActivitiesDocuments> GetListRelative<T>(this List<T> docList, string docType)
        {
            List<RelevantActivitiesDocuments> Items = new List<RelevantActivitiesDocuments>();

            Guid outGuid = Guid.Empty;
            foreach (var item in docList)
            {
                var type = item.GetType();
                var id = type.GetProperty("Id").GetValue(item, null);
                var docid = type.GetProperty("DocumentsId").GetValue(item);
                var docItem = new RelevantActivitiesDocuments();

                docItem.Id = id == null ? outGuid : (Guid)id;
                docItem.ContetntType = type.GetProperty("ContetntType").GetValue(item, null) as string;
                docItem.FileName = type.GetProperty("FileName").GetValue(item, null) as string;
                docItem.DocumentCategory = docType;
                docItem.DocumentsId = docid == null ? outGuid : (Guid)docid;
                Items.Add(docItem);
            }
            return Items;
        }
        static List<RelevantActivityDetailInformations> GetInformationDetail<T, R>(this List<T> detailList, string detailType)
        {
            Guid outGuid = Guid.Empty;
            List<RelevantActivityDetailInformations> result = new List<RelevantActivityDetailInformations>();
            detailList.ForEach(x =>
            {
                var type = x.GetType();
                var isNew = (bool)type.GetProperty("IsNew").GetValue(x, null);
                var id = type.GetProperty("Id").GetValue(x, null);
                var isDeleted = type.GetProperty("IsDeleted").GetValue(x, null);
                if (isNew)
                {
                    result.Add(new RelevantActivityDetailInformations
                    {
                        DetailMetaData = JsonConvert.SerializeObject(Mapper.Map<R>(x)),
                        IsDeleted = (bool)isDeleted,
                        DetailType = detailType
                    });
                }
                else
                    result.Add(new RelevantActivityDetailInformations
                    {
                        Id = id == null ? outGuid : (Guid)id,
                        DetailMetaData = JsonConvert.SerializeObject(Mapper.Map<R>(x)),
                        IsDeleted = (bool)isDeleted,
                        DetailType = detailType
                    });
            });


            return result;
        }

        static List<T> GetEachTypeInformationDetail<T>(this List<RelevantActivityDetailInformations> detailList, string detailType)
        {
            List<T> result = new List<T>();
            detailList.Where(x => x.DetailType == detailType && !x.IsDeleted).ToList().ForEach(x =>
            {

                var re = JsonConvert.DeserializeObject<T>(x.DetailMetaData);
                var idx = (object)x.Id;
                re.GetType().GetProperty("Id").SetValue(re, idx, null);
                result.Add(re);

            });
            return result;
        }



        static public List<RelevantActivityDetailDto> AddMissingRelevantActivity(List<RelevantActivityDetailDto> input)
        {
            if (input == null) input = new List<RelevantActivityDetailDto>();

            var values = input.Where(x => !x.IsDeleted).Select(x => x.ReleventActivityValue).ToList();

            if (!values.Contains(RelevantActivity.None))
            {
                input.Add(new RelevantActivityDetailDto
                {
                    RelevantActivityName = RelevantActivity.None.GetDescription(),
                    ReleventActivityValue = RelevantActivity.None,
                    IsChecked = false
                });
            }

            List<RelevantActivity> excludedActivity = new List<RelevantActivity>() { RelevantActivity.None };
            var relevantActivityList = Enum.GetValues(typeof(RelevantActivity)).Cast<RelevantActivity>().Where(i => !excludedActivity.Any(e => e == i)).ToList();
            relevantActivityList.ForEach(r =>
            {
                if (!values.Contains(r))
                {
                    input.Add(NewRelevantActivityDetailDto(r));
                }
            });

            input = input.OrderBy(x => (int)x.ReleventActivityValue).ToList();
            return input;
        }

        // <summary>
        /// Map from RelevantActivity to RelevantActivityDetailDto
        /// </summary>
        /// <param name="activity"></param>
        /// <returns></returns>
        private static RelevantActivityDetailDto NewRelevantActivityDetailDto(RelevantActivity activity)
        {
            return new RelevantActivityDetailDto
            {
                RelevantActivityName = activity.GetDescription(),
                ReleventActivityValue = activity,
                IsChecked = false,
                PremisesAddress = new List<AddressDto>(),
                ConductedCIGAActivity = new List<CIGARelevantActivityDto>(),
                ServiceProviders = new List<ServiceProvidersDto>(),
                ManagementDetails = new List<PersonDetailsDto>(),
                AttendMeetingDetails = new List<AttendMeetingDetailsDto>(),
                EmployeeQualificationDetails = new List<EmployeeQualificationDetailsDto>(),
                OtherCIGADocuments = new List<OtherCIGADocumentsDto>(),
                HighRiskDocuments = new List<HighRiskDocumentsDto>(),
                TangibleAssetIncomeDocuments = new List<GenericRelevantActivityDocumentDto>(),
                TangibleAssetEmployeeResponsibilityDocuments = new List<GenericRelevantActivityDocumentDto>(),
                HistoryofStrategicDecisionsInBVIDocuments = new List<GenericRelevantActivityDocumentDto>(),
                HistoryofTradingActivityIncomeDocuments = new List<GenericRelevantActivityDocumentDto>(),
                IPAssetsInBVIDocuments = new List<GenericRelevantActivityDocumentDto>(),
                IPAssetsEmployeeResponsibilityDocuments = new List<GenericRelevantActivityDocumentDto>(),
                ConcreteEvidenceDecisionInBVIDocuments = new List<GenericRelevantActivityDocumentDto>()
            };
        }


        private static List<EconomicSubstanceInformationRequiredDto> MapToInformationRequirementDto(this List<EconomicSubstanceInformationRequired> input)
        {
            List<EconomicSubstanceInformationRequiredDto> resultList = new List<EconomicSubstanceInformationRequiredDto>();
            if (input == null) return resultList;
            foreach (var item in input)
            {
                EconomicSubstanceInformationRequiredDto result = new EconomicSubstanceInformationRequiredDto();
                result.IsInformationRequired = item.IsInformationRequired;
                result.InformationRequestedDueDate = item.InformationRequestedDueDate;
                result.InRequestedDueDateString = item.InformationRequestedDueDate?.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
                result.InformationRequestedCommentsCA = item.InformationRequestedCommentsCA;
                result.CreatedAt = item.CreatedAt;
                result.CreatedBy = item.CreatedBy;
                result.UpdatedAt = item.UpdatedAt;
                result.UpdatedBy = item.UpdatedBy;
                result.InformationRequestDocumentCas =
                    Mapper.Map<List<EsInformationRequestDocumentCa>, List<EsInformationRequestDocumentCaDto>>(item.EsInformationRequestDocumentCas.ToList());

                result.InformationRequestedSubmissionDate = item.InformationRequestedSubmissionDate;
                result.InformationRequestedCommentsCTSP = item.InformationRequestedCommentsCTSP;
                result.InformationRequestedSubmittedBy = item.InformationRequestedSubmittedBy;
                result.EconomicSubstanceDeclarationId = item.EconomicSubstanceDeclarationId;
                result.Id = item.Id;
                result.EconomicSubstanceInformationRequiredDocuments = Mapper.Map<List<EconomicSubstanceInformationRequiredDocumentsDto>>(item.EconomicSubstanceInformationRequiredDocuments.Where(x => !x.IsDeleted).ToList());

                resultList.Add(result);
            }
            return resultList;
        }

        private static List<EconomicSubstanceInformationRequired> MapToInformationRequirement(this List<EconomicSubstanceInformationRequiredDto> input)
        {
            List<EconomicSubstanceInformationRequired> resultList = new List<EconomicSubstanceInformationRequired>();
            if (input == null) return resultList;
            foreach (var item in input)
            {
                EconomicSubstanceInformationRequired result = new EconomicSubstanceInformationRequired();
                result.Id = item.Id;
                result.IsInformationRequired = item.IsInformationRequired;
                result.InformationRequestedDueDate = item.InformationRequestedDueDate;
                result.InformationRequestedCommentsCA = item.InformationRequestedCommentsCA;
                result.InformationRequestedSubmissionDate = item.InformationRequestedSubmissionDate;
                result.InformationRequestedCommentsCTSP = item.InformationRequestedCommentsCTSP;
                result.InformationRequestedSubmittedBy = item.InformationRequestedSubmittedBy;
                result.EconomicSubstanceDeclarationId = item.EconomicSubstanceDeclarationId;

                var docs = item.EconomicSubstanceInformationRequiredDocuments.Where(x => !x.IsDeleted).ToList();
                foreach (var x in docs)
                {
                    EconomicSubstanceInformationRequiredDocuments temp = new EconomicSubstanceInformationRequiredDocuments();
                    temp.DocumentsId = x.DocumentsId;
                    temp.FileName = x.FileName;
                    temp.ContetntType = x.ContetntType;
                    temp.EconomicSubstanceInformationRequiredId = x.EconomicSubstanceInformationRequiredId;
                }


                resultList.Add(result);
            }

            return resultList;
        }

        /// <summary>
        /// Map from EconomicSubstanceDeclaration to EconomicSubstanceDeclarationDto
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public static EconomicSubstanceDeclarationDto MapToEconomicSubstanceDto(this EconomicSubstanceDeclaration input, bool includeDetail = true)
        {
            EconomicSubstanceDeclarationDto result = new EconomicSubstanceDeclarationDto();
            result.Id = input.Id;
            result.IsDeleted = input.IsDeleted;
            result.CorporateEntityId = input.CorporateEntityId;
            result.CorporateEntity = _corporateEntity;
            result.IsFinaincialCanChange = input.IsFinaincialCanChange;
            result.FiscalStartDate = input.FiscalStartDate;
            result.FiscalEndDate = input.FiscalEndDate;


            result.FiscalStartDateString = input.FiscalStartDate.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
            result.FiscalEndDateString = input.FiscalEndDate.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);



            result.AnyRelevantActivtyDuringFiscalYear = input.AnyRelevantActivtyDuringFiscalYear;
            result.EvidenceOfNonResidanceDocument = input.EconomicSubstanceDocuments != null ? Mapper.Map<List<EvidenceNonResidencyDocumentsDto>>(input.EconomicSubstanceDocuments.Where(x => x.DocumentCategory == EvidenceNonResidency && !x.IsDeleted).ToList()) : null;
            result.EvidenceProvisionalTreatmentDocuments = input.EconomicSubstanceDocuments != null ? Mapper.Map<List<EvidenceProvisionalTreatmentDocumentsDto>>(input.EconomicSubstanceDocuments.Where(x => x.DocumentCategory == EvidenceProvisionalTreatment && !x.IsDeleted).ToList()) : null;
            result.SupportingDocument = input.EconomicSubstanceDocuments != null ? Mapper.Map<List<SupportingDocumentDto>>(input.EconomicSubstanceDocuments.Where(x => x.DocumentCategory == SupportingDocument && !x.IsDeleted).ToList()) : null;
            result.DoesEntityMakeClaimOutisedBVI = input.DoesEntityMakeClaimOutisedBVI;
            result.JurisdictionTaxResidentId = input.JurisdictionTaxResidentId;
            result.JurisdictionTaxResident = _countryDict?.ContainsKey(result.JurisdictionTaxResidentId ?? Guid.Empty) == true ? _countryDict[result.JurisdictionTaxResidentId ?? Guid.Empty] : null;
            result.DoesEntityHaveParentEntity = input.DoesEntityHaveParentEntity;
            result.ParentEntityName = input.ParentEntityName;
            result.ParentEntityAlternativeName = input.ParentEntityAlternativeName;
            result.ParentJurisdictionId = input.ParentJurisdictionId;
            result.EntityIncorporationNumber = input.EntityIncorporationNumber;
            result.ParentJurisdiction = _countryDict?.ContainsKey(result.ParentJurisdictionId ?? Guid.Empty) == true ? _countryDict[result.ParentJurisdictionId ?? Guid.Empty] : null;
            result.IsEvidenceNonResidenceOrTreatment = input.IsEvidenceNonResidenceOrTreatment;
            result.SupportingComments = input.SupportingComments;
            result.Status = input.Status;
            result.SubmissionDate = input.SubmissionDate;
            result.SubmitterName = input.SubmitterName;

            result.CurrencyId = input.CurrencyId;
            result.Currency = _currencyDict?.ContainsKey(result.CurrencyId ?? Guid.Empty) == true ? _currencyDict[result.CurrencyId ?? Guid.Empty] : null;

            result.CurrencyExchangeRate = input.CurrencyExchangeRate;
            result.IsEmailSentToReopen = input.IsEmailSentToReopen;
            if (includeDetail)
            {
                result.RelevantActivities = input.RelevantActivities != null ? input.RelevantActivities.Where(x => x.IsChecked && !x.IsDeleted).Select(x => { return x.MapToRelevantActivityDetailDto(); }).ToList() : null;
                result.RelevantActivities = AddMissingRelevantActivity(result.RelevantActivities);
            }

            // provisional treatment 

            result.ApproveRejectProvisionalTreatment = input.ApproveRejectProvisionalTreatment;
            result.PrTreatmentDueDateString = input.ProvisionalTreatmentDueDate?.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);

            result.ProvisionalTreatmentDueDate = input.ProvisionalTreatmentDueDate;
            result.ProvisionalTreatmentDocument = input.EconomicSubstanceDocuments != null ? Mapper.Map<List<ProvisionalTreatmentDocumentDto>>(input.EconomicSubstanceDocuments.Where(x => x.DocumentCategory == ProvisionalTreatmentDocument && !x.IsDeleted).ToList()) : null;
            result.ProvisionalTreatmentSubmittedBy = input.ProvisionalTreatmentSubmittedBy;
            result.ProvisionalTreatmentSubmissionDate = input.ProvisionalTreatmentSubmissionDate;
            //
            // Information Request 

            result.EconomicSubstanceInformationRequired = input.EconomicSubstanceInformationRequired?.ToList().MapToInformationRequirementDto();

            // Entity Details
            result.EsEntityDetails = input.EsEntityDetails?.Where(x => !x.IsDeleted && x.CorporateEntityId != Guid.Empty).Select(x => { return x.MapToEsEntityDetailDto(); }).ToList();

            // Tax Residency 2.0
            result.TaxPayerIdentificationNumber = input.TaxPayerIdentificationNumber;


            return result;
        }


        public static List<EconomicSubstanceDeclarationDto> MapToEconomicSubstanceDtoList(this List<EconomicSubstanceDeclaration> input, bool incldeDetail = true)
        {
            List<EconomicSubstanceDeclarationDto> result = new List<EconomicSubstanceDeclarationDto>();
            input.ForEach(x => { result.Add(x.MapToEconomicSubstanceDto(incldeDetail)); });
            return result;
        }

        // <summary>
        /// Map from EconomicSubstanceDeclarationDto to EconomicSubstanceDeclaration
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static EconomicSubstanceDeclaration MapToEconomicSubstance(this EconomicSubstanceDeclarationDto input)
        {

            EconomicSubstanceDeclaration result = new EconomicSubstanceDeclaration();
            result.EconomicSubstanceDocuments = new List<EconomicSubstanceDocuments>();
            result.Id = input.Id;
            result.IsDeleted = input.IsDeleted;
            result.CorporateEntityId = input.CorporateEntityId;
            result.IsFinaincialCanChange = input.IsFinaincialCanChange;
            result.FiscalStartDate = input.FiscalStartDate;
            result.FiscalEndDate = input.FiscalEndDate;
            result.AnyRelevantActivtyDuringFiscalYear = input.AnyRelevantActivtyDuringFiscalYear;

            if (input.EvidenceProvisionalTreatmentDocuments != null) input.EvidenceProvisionalTreatmentDocuments.ToList().GetList(EvidenceProvisionalTreatment).ForEach(x => result.EconomicSubstanceDocuments.Add(x));
            if (input.EvidenceOfNonResidanceDocument != null) input.EvidenceOfNonResidanceDocument.ToList().GetList(EvidenceNonResidency).ForEach(x => result.EconomicSubstanceDocuments.Add(x));
            if (input.SupportingDocument != null) input.SupportingDocument.ToList().GetList(SupportingDocument).ForEach(x => result.EconomicSubstanceDocuments.Add(x));


            result.DoesEntityMakeClaimOutisedBVI = input.DoesEntityMakeClaimOutisedBVI;
            result.JurisdictionTaxResidentId = input.JurisdictionTaxResidentId;
            result.DoesEntityHaveParentEntity = input.DoesEntityHaveParentEntity;
            result.ParentEntityName = input.ParentEntityName;
            result.ParentEntityAlternativeName = input.ParentEntityAlternativeName;
            result.ParentJurisdictionId = input.ParentJurisdictionId;
            result.EntityIncorporationNumber = input.EntityIncorporationNumber;
            result.IsEvidenceNonResidenceOrTreatment = input.IsEvidenceNonResidenceOrTreatment;
            result.SupportingComments = input.SupportingComments;
            result.Status = input.Status;
            result.SubmissionDate = input.SubmissionDate;
            result.SubmitterName = input.SubmitterName;
            if (input.CurrencyId == null && input.Currency != null) input.CurrencyId = input.Currency.Id;
            result.CurrencyId = input.CurrencyId;
            result.CurrencyExchangeRate = input.CurrencyExchangeRate;
            if (input.RelevantActivities != null)
            {
                var allRelevantActivity = input.RelevantActivities.Select(x => { return x.MapToRelevantActivityDetail(); }).ToList();
                // need to update all the unchecked exisiting relative activity + the new check one
                result.RelevantActivities = allRelevantActivity.Where(x => x.IsChecked).Union(allRelevantActivity.Where(x => !x.IsChecked && x.Id != Guid.Empty)).ToList();
            }

            // provisional Treatment 
            result.ProvisionalTreatmentSubmittedBy = input.ProvisionalTreatmentSubmittedBy;
            result.ApproveRejectProvisionalTreatment = input.ApproveRejectProvisionalTreatment;
            if (input.ProvisionalTreatmentDocument != null) input.ProvisionalTreatmentDocument.ToList().GetList(ProvisionalTreatmentDocument).ForEach(x => result.EconomicSubstanceDocuments.Add(x));
            result.ProvisionalTreatmentSubmissionDate = input.ProvisionalTreatmentSubmissionDate;
            result.ProvisionalTreatmentDueDate = input.ProvisionalTreatmentDueDate;

            // Information Request 
            result.EconomicSubstanceInformationRequired = input.EconomicSubstanceInformationRequired.MapToInformationRequirement();

            // Entity Details 
            result.EsEntityDetails = Mapper.Map<List<EsEntityDetail>>(input.EsEntityDetails?.Where(x => !x.IsDeleted).ToList());

            // Tax Residency 2.0
            result.TaxPayerIdentificationNumber = input.TaxPayerIdentificationNumber;

            return result;
        }

        private static List<Address> MapAddress(this List<AddressDto> input)
        {
            List<Address> result = new List<Address>();
            if (input != null)
                input.ForEach(x =>
               {
                   Address temp = new Address
                   {
                       AddressLine1 = x.AddressLine1,
                       AddressLine2 = x.AddressLine2,

                       CountryId = x.CountryId,
                       IsDeleted = x.IsDeleted,
                       IsActive = x.IsActive
                   };
                   if (!x.IsNew) temp.Id = x.Id;
                   result.Add(temp);
               });
            return result;
        }

        private static List<CIGARelevantActivityDto> PopulateCIGAId(this List<CIGARelevantActivityDto> input)
        {
            if (input == null) return input;
            input.ForEach(x => x.CIGAActivityId = x.CIGAActivity != null ? x.CIGAActivity.Id : Guid.Empty);
            return input;
        }
        public static RelevantActivityDetail MapToRelevantActivityDetail(this RelevantActivityDetailDto input)
        {
            RelevantActivityDetail result = new RelevantActivityDetail();
            result.RelevantActivityDetailInformations = new List<RelevantActivityDetailInformations>();
            result.RelevantActivitiesDocuments = new List<RelevantActivitiesDocuments>();
            result.Id = input.Id;
            result.EconomicSubstanceDeclarationId = input.EconomicSubstanceDeclarationId;
            result.RelevantActivityName = input.RelevantActivityName;
            result.ReleventActivityValue = input.ReleventActivityValue;
            result.IsChecked = input.IsChecked;
            result.IsCarriedForPartFinancialPeriod = input.IsCarriedForPartFinancialPeriod;
            result.IsGrossIncomeEarned = input.IsGrossIncomeEarned;
            result.StartDate = input.StartDate;
            result.EndDate = input.EndDate;
            result.IsActivityDirectedInBVI = input.IsActivityDirectedInBVI;
            result.NoofConductedMeeting = input.NoofConductedMeeting;
            result.NoofMeetingHeldInBVI = input.NoofMeetingHeldInBVI;
            result.IsMeetingMinutesInBVI = input.IsMeetingMinutesInBVI;
            result.TotalTurnover = input.TotalTurnover;
            result.TotalExpediture = input.TotalExpediture;
            result.TotalExpeditureInBVI = input.TotalExpeditureInBVI;
            result.TotalNoFullTimeEmployee = input.TotalNoFullTimeEmployee;
            result.TotalFullTimeEmployeeInBVI = input.TotalFullTimeEmployeeInBVI;
            result.CIGAOtherDetail = input.CIGAOtherDetail;
            result.HasAnyIncomeBeenOutsourced = input.HasAnyIncomeBeenOutsourced;
            result.WasCIGAOutsourcedInBVI = input.WasCIGAOutsourcedInBVI;
            result.TotalExpenditureIncurredInBVI = input.TotalExpenditureIncurredInBVI;

            result.ConductedCIGAActivity = Mapper.Map<List<CIGARelevantActivity>>(input.ConductedCIGAActivity.PopulateCIGAId());

            result.PremisesAddress = input.PremisesAddress.MapAddress();

            if (input.ServiceProviders != null) GetInformationDetail<ServiceProvidersDto, ServiceProvideMetaData>(input.ServiceProviders, ServiceProvidersType).ForEach(x => result.RelevantActivityDetailInformations.Add(x));
            if (input.ManagementDetails != null) GetInformationDetail<PersonDetailsDto, PersonalDetailMetaData>(input.ManagementDetails, ManagementDetailsType).ForEach(x => result.RelevantActivityDetailInformations.Add(x));
            if (input.AttendMeetingDetails != null) GetInformationDetail<AttendMeetingDetailsDto, AttendMeetingDetailsMetaData>(input.AttendMeetingDetails, AttendMeetingDetailsType).ForEach(x => result.RelevantActivityDetailInformations.Add(x));
            if (input.EmployeeQualificationDetails != null) GetInformationDetail<EmployeeQualificationDetailsDto, EmployeeQualificationDetailsMetaData>(input.EmployeeQualificationDetails, EmployeeQualificationDetailsType).ForEach(x => result.RelevantActivityDetailInformations.Add(x));

            // holding part
            result.DoesEntityComplyItsStatutoryObligations = input.DoesEntityComplyItsStatutoryObligations;
            result.DoesEntityManageEquity = input.DoesEntityManageEquity;
            result.DoesEntityHaveAdequateEmployee = input.DoesEntityHaveAdequateEmployee;

            // intelect
            result.IsLegalEntityHighRisk = input.IsLegalEntityHighRisk;
            result.DoesEntityProvideEvidence = input.DoesEntityProvideEvidence;
            result.TotalGrossAnnualIncome = input.TotalGrossAnnualIncome;
            result.GrossIncomeRoyalities = input.GrossIncomeRoyalities;
            result.GrossIncomeGains = input.GrossIncomeGains;
            result.GrossIncomeOthers = input.GrossIncomeOthers;
            result.DoesLegalEntityConductCIGA = input.DoesLegalEntityConductCIGA;
            result.DoesEntityProvideEvidenceroRebut = input.DoesEntityProvideEvidenceroRebut;
            result.DoesBusinessRequireEquipment = input.DoesBusinessRequireEquipment;
            result.EquipmentInJurisdiction = input.EquipmentInJurisdiction;
            result.EquipmentDescription = input.EquipmentDescription;


            if (input.OtherCIGADocuments != null)
                input.OtherCIGADocuments.ToList().GetListRelative(OtherCIGADocuments).ForEach(x => result.RelevantActivitiesDocuments.Add(x));
            if (input.HighRiskDocuments != null)
                input.HighRiskDocuments.ToList().GetListRelative(HighRiskDocuments).ForEach(x => result.RelevantActivitiesDocuments.Add(x));

            result.IsDeleted = !input.IsChecked;

            // CR 2.0           
            result.GrossIncomeType = input.GrossIncomeType;
            result.TotalAssetsValue = input.TotalAssetsValue;
            result.NetAssetsValue = input.NetAssetsValue;
            result.NoofQuorumBoardMeeting = input.NoofQuorumBoardMeeting;
            result.IsQuorumBoardMeetingInBVI = input.IsQuorumBoardMeetingInBVI;
            result.TotalNoCorporateLegalEmployee = input.TotalNoCorporateLegalEmployee;
            result.TangibleAsset = input.TangibleAsset;
            result.TangibleAssetIncome = input.TangibleAssetIncome;
            result.TangibleAssetEmployeeResponsibility = input.TangibleAssetEmployeeResponsibility;
            result.HistoryofStrategicDecisionsInBVI = input.HistoryofStrategicDecisionsInBVI;
            result.HistoryofTradingActivityIncome = input.HistoryofTradingActivityIncome;

            result.RelevantIPAsset = input.RelevantIPAsset;
            result.IPAssetsInBVI = input.IPAssetsInBVI;
            result.IPAssetsEmployeeResponsibility = input.IPAssetsEmployeeResponsibility;
            result.ConcreteEvidenceDecisionInBVI = input.ConcreteEvidenceDecisionInBVI;

            if (input.TangibleAssetIncomeDocuments != null)
                input.TangibleAssetIncomeDocuments.ToList().GetListRelative(TangibleAssetIncomeDocuments).ForEach(x => result.RelevantActivitiesDocuments.Add(x));
            if (input.TangibleAssetEmployeeResponsibilityDocuments != null)
                input.TangibleAssetEmployeeResponsibilityDocuments.ToList().GetListRelative(TangibleAssetEmployeeResponsibilityDocuments).ForEach(x => result.RelevantActivitiesDocuments.Add(x));
            if (input.HistoryofStrategicDecisionsInBVIDocuments != null)
                input.HistoryofStrategicDecisionsInBVIDocuments.ToList().GetListRelative(HistoryofStrategicDecisionsInBVIDocuments).ForEach(x => result.RelevantActivitiesDocuments.Add(x));
            if (input.HistoryofTradingActivityIncomeDocuments != null)
                input.HistoryofTradingActivityIncomeDocuments.ToList().GetListRelative(HistoryofTradingActivityIncomeDocuments).ForEach(x => result.RelevantActivitiesDocuments.Add(x));
            if (input.IPAssetsInBVIDocuments != null)
                input.IPAssetsInBVIDocuments.ToList().GetListRelative(IPAssetsInBVIDocuments).ForEach(x => result.RelevantActivitiesDocuments.Add(x));
            if (input.IPAssetsEmployeeResponsibilityDocuments != null)
                input.IPAssetsEmployeeResponsibilityDocuments.ToList().GetListRelative(IPAssetsEmployeeResponsibilityDocuments).ForEach(x => result.RelevantActivitiesDocuments.Add(x));
            if (input.ConcreteEvidenceDecisionInBVIDocuments != null)
                input.ConcreteEvidenceDecisionInBVIDocuments.ToList().GetListRelative(ConcreteEvidenceDecisionInBVIDocuments).ForEach(x => result.RelevantActivitiesDocuments.Add(x));

            return result;
        }

        public static RelevantActivityDetailDto MapToRelevantActivityDetailDto(this RelevantActivityDetail input)
        {
            RelevantActivityDetailDto result = new RelevantActivityDetailDto();
            result.Id = input.Id;
            result.EconomicSubstanceDeclarationId = input.EconomicSubstanceDeclarationId;
            result.RelevantActivityName = input.RelevantActivityName;
            result.ReleventActivityValue = input.ReleventActivityValue;
            result.IsChecked = input.IsChecked;
            result.IsCarriedForPartFinancialPeriod = input.IsCarriedForPartFinancialPeriod;
            result.IsGrossIncomeEarned = input.IsGrossIncomeEarned;
            result.StartDate = input.StartDate;
            result.EndDate = input.EndDate;

            result.StartDateString = input.StartDate.HasValue ? input.StartDate.Value.ToString("dd/MM/yyyy") : string.Empty;
            result.EndDateString = input.EndDate.HasValue ? input.EndDate.Value.ToString("dd/MM/yyyy") : string.Empty;

            result.IsActivityDirectedInBVI = input.IsActivityDirectedInBVI;
            result.NoofConductedMeeting = input.NoofConductedMeeting;
            result.NoofMeetingHeldInBVI = input.NoofMeetingHeldInBVI;
            result.IsMeetingMinutesInBVI = input.IsMeetingMinutesInBVI;
            result.TotalTurnover = input.TotalTurnover;
            result.TotalExpediture = input.TotalExpediture;
            result.TotalExpeditureInBVI = input.TotalExpeditureInBVI;
            result.TotalNoFullTimeEmployee = input.TotalNoFullTimeEmployee;
            result.TotalFullTimeEmployeeInBVI = input.TotalFullTimeEmployeeInBVI;
            result.CIGAOtherDetail = input.CIGAOtherDetail;
            result.HasAnyIncomeBeenOutsourced = input.HasAnyIncomeBeenOutsourced;
            result.WasCIGAOutsourcedInBVI = input.WasCIGAOutsourcedInBVI;
            result.TotalExpenditureIncurredInBVI = input.TotalExpenditureIncurredInBVI;
            result.ConductedCIGAActivity = Mapper.Map<List<CIGARelevantActivityDto>>(input.ConductedCIGAActivity.Where(x => !x.IsDeleted));
            result.ConductedCIGAActivity = Mapper.Map<List<CIGARelevantActivityDto>>(input.ConductedCIGAActivity);

            if (result.ConductedCIGAActivity != null)
            {
                result.ConductedCIGAActivity.ForEach(x => x.CIGAActivity = x.CIGAActivityId != Guid.Empty && _cigaDict.ContainsKey(x.CIGAActivityId) ? _cigaDict[x.CIGAActivityId] : null);
                result.ConductedCIGAActivity = result.ConductedCIGAActivity.Where(x => x.CIGAActivity != null).ToList();
            }

            result.ServiceProviders = input.RelevantActivityDetailInformations != null ? input.RelevantActivityDetailInformations.ToList().GetEachTypeInformationDetail<ServiceProvidersDto>(ServiceProvidersType) : null;
            result.ManagementDetails = input.RelevantActivityDetailInformations != null ? input.RelevantActivityDetailInformations.ToList().GetEachTypeInformationDetail<PersonDetailsDto>(ManagementDetailsType) : null;
            result.AttendMeetingDetails = input.RelevantActivityDetailInformations != null ? input.RelevantActivityDetailInformations.ToList().GetEachTypeInformationDetail<AttendMeetingDetailsDto>(AttendMeetingDetailsType) : null;
            result.EmployeeQualificationDetails = input.RelevantActivityDetailInformations != null ? input.RelevantActivityDetailInformations.ToList().GetEachTypeInformationDetail<EmployeeQualificationDetailsDto>(EmployeeQualificationDetailsType) : null;
            result.PremisesAddress = Mapper.Map<List<AddressDto>>(input.PremisesAddress);
            // get the country of the Premises Address
            result.PremisesAddress.ForEach(x => { x.Country = _countryDict != null && _countryDict.ContainsKey(x.CountryId) ? _countryDict[x.CountryId] : null; });
            // holding part
            result.DoesEntityComplyItsStatutoryObligations = input.DoesEntityComplyItsStatutoryObligations;
            result.DoesEntityManageEquity = input.DoesEntityManageEquity;
            result.DoesEntityHaveAdequateEmployee = input.DoesEntityHaveAdequateEmployee;

            // intelect
            result.IsLegalEntityHighRisk = input.IsLegalEntityHighRisk;
            result.DoesEntityProvideEvidence = input.DoesEntityProvideEvidence;
            result.TotalGrossAnnualIncome = input.TotalGrossAnnualIncome;
            result.GrossIncomeRoyalities = input.GrossIncomeRoyalities;
            result.GrossIncomeGains = input.GrossIncomeGains;
            result.GrossIncomeOthers = input.GrossIncomeOthers;
            result.DoesLegalEntityConductCIGA = input.DoesLegalEntityConductCIGA;
            result.DoesEntityProvideEvidenceroRebut = input.DoesEntityProvideEvidenceroRebut;
            result.DoesBusinessRequireEquipment = input.DoesBusinessRequireEquipment;
            result.EquipmentInJurisdiction = input.EquipmentInJurisdiction;
            result.EquipmentDescription = input.EquipmentDescription;

            result.OtherCIGADocuments = input.RelevantActivitiesDocuments != null ? Mapper.Map<List<OtherCIGADocumentsDto>>(input.RelevantActivitiesDocuments.Where(x => x.DocumentCategory == OtherCIGADocuments && !x.IsDeleted).ToList()) : null;
            result.HighRiskDocuments = input.RelevantActivitiesDocuments != null ? Mapper.Map<List<HighRiskDocumentsDto>>(input.RelevantActivitiesDocuments.Where(x => x.DocumentCategory == HighRiskDocuments && !x.IsDeleted).ToList()) : null;

            // CR 2.0            
            result.GrossIncomeType = input.GrossIncomeType;
            result.TotalAssetsValue = input.TotalAssetsValue;
            result.NetAssetsValue = input.NetAssetsValue;
            result.NoofQuorumBoardMeeting = input.NoofQuorumBoardMeeting;
            result.IsQuorumBoardMeetingInBVI = input.IsQuorumBoardMeetingInBVI;
            result.TotalNoCorporateLegalEmployee = input.TotalNoCorporateLegalEmployee;
            result.TangibleAsset = input.TangibleAsset;
            result.TangibleAssetIncome = input.TangibleAssetIncome;
            result.TangibleAssetEmployeeResponsibility = input.TangibleAssetEmployeeResponsibility;
            result.HistoryofStrategicDecisionsInBVI = input.HistoryofStrategicDecisionsInBVI;
            result.HistoryofTradingActivityIncome = input.HistoryofTradingActivityIncome;

            result.RelevantIPAsset = input.RelevantIPAsset;
            result.IPAssetsInBVI = input.IPAssetsInBVI;
            result.IPAssetsEmployeeResponsibility = input.IPAssetsEmployeeResponsibility;
            result.ConcreteEvidenceDecisionInBVI = input.ConcreteEvidenceDecisionInBVI;

            result.TangibleAssetIncomeDocuments = Mapper.Map<List<GenericRelevantActivityDocumentDto>>(input.RelevantActivitiesDocuments?.Where(x => x.DocumentCategory == TangibleAssetIncomeDocuments && !x.IsDeleted).ToList());
            result.TangibleAssetEmployeeResponsibilityDocuments = Mapper.Map<List<GenericRelevantActivityDocumentDto>>(input.RelevantActivitiesDocuments?.Where(x => x.DocumentCategory == TangibleAssetEmployeeResponsibilityDocuments && !x.IsDeleted).ToList());
            result.HistoryofStrategicDecisionsInBVIDocuments = Mapper.Map<List<GenericRelevantActivityDocumentDto>>(input.RelevantActivitiesDocuments?.Where(x => x.DocumentCategory == HistoryofStrategicDecisionsInBVIDocuments && !x.IsDeleted).ToList());
            result.HistoryofTradingActivityIncomeDocuments = Mapper.Map<List<GenericRelevantActivityDocumentDto>>(input.RelevantActivitiesDocuments?.Where(x => x.DocumentCategory == HistoryofTradingActivityIncomeDocuments && !x.IsDeleted).ToList());
            result.IPAssetsInBVIDocuments = Mapper.Map<List<GenericRelevantActivityDocumentDto>>(input.RelevantActivitiesDocuments?.Where(x => x.DocumentCategory == IPAssetsInBVIDocuments && !x.IsDeleted).ToList());
            result.IPAssetsEmployeeResponsibilityDocuments = Mapper.Map<List<GenericRelevantActivityDocumentDto>>(input.RelevantActivitiesDocuments?.Where(x => x.DocumentCategory == IPAssetsEmployeeResponsibilityDocuments && !x.IsDeleted).ToList());
            result.ConcreteEvidenceDecisionInBVIDocuments = Mapper.Map<List<GenericRelevantActivityDocumentDto>>(input.RelevantActivitiesDocuments?.Where(x => x.DocumentCategory == ConcreteEvidenceDecisionInBVIDocuments && !x.IsDeleted).ToList());

            return result;
        }


        public static EconomicSubstanceRelevantActivityAuditDto MapToRelevantActivityDetailAuditDto(this RelevantActivityDetail input)
        {
            EconomicSubstanceRelevantActivityAuditDto result = new EconomicSubstanceRelevantActivityAuditDto();
            result.Id = input.Id;
            result.EconomicSubstanceDeclarationId = input.EconomicSubstanceDeclarationId;
            result.RelevantActivityCode = input.ReleventActivityValue.GetAmbientValue();
            result.RelevantActivityName = input.RelevantActivityName;

            result.IsChecked = input.IsChecked;
            result.IsCarriedForPartFinancialPeriod = input.IsCarriedForPartFinancialPeriod;
            result.IsGrossIncomeEarned = input.IsGrossIncomeEarned;
            result.StartDate = input.StartDate;
            result.EndDate = input.EndDate;
            result.IsActivityDirectedInBVI = input.IsActivityDirectedInBVI;
            result.NoofConductedMeeting = input.NoofConductedMeeting;
            result.NoofMeetingHeldInBVI = input.NoofMeetingHeldInBVI;
            result.IsMeetingMinutesInBVI = input.IsMeetingMinutesInBVI;
            result.TotalTurnover = input.TotalTurnover;
            result.TotalExpediture = input.TotalExpediture;
            result.TotalExpeditureInBVI = input.TotalExpeditureInBVI;
            result.TotalNoFullTimeEmployee = input.TotalNoFullTimeEmployee;
            result.TotalFullTimeEmployeeInBVI = input.TotalFullTimeEmployeeInBVI;
            result.CIGAOtherDetail = input.CIGAOtherDetail;
            result.HasAnyIncomeBeenOutsourced = input.HasAnyIncomeBeenOutsourced;
            result.WasCIGAOutsourcedInBVI = input.WasCIGAOutsourcedInBVI;
            result.TotalExpenditureIncurredInBVI = input.TotalExpenditureIncurredInBVI;

            result.ConductedCIGAActivity = Mapper.Map<List<EconomicSubstanceCIGAAuditDto>>(input.ConductedCIGAActivity);
            result.ConductedCIGAActivity.ForEach(x =>
            {
                if (x.CIGAActivityId != Guid.Empty)
                {
                    _cigaDict.TryGetValue(x.CIGAActivityId, out CIGALookUpDto cigaCode);
                    x.CIGAText = cigaCode != null ? cigaCode.ActivityDetails : string.Empty;
                    x.CIGACode = cigaCode != null ? cigaCode.ActivityOrder : 0;
                }
            }

            );

            result.ServiceProviders = input.RelevantActivityDetailInformations != null ? input.RelevantActivityDetailInformations.ToList().GetEachTypeInformationDetail<EconomicSubstanceOutsourceProviderAuditDto>(ServiceProvidersType) : null;
            result.ManagementDetails = input.RelevantActivityDetailInformations != null ? input.RelevantActivityDetailInformations.ToList().GetEachTypeInformationDetail<EconomicSubstancePeronsalDetailsAuditDto>(ManagementDetailsType) : null;
            result.AttendMeetingDetails = input.RelevantActivityDetailInformations != null ? input.RelevantActivityDetailInformations.ToList().GetEachTypeInformationDetail<EconomicSubstanceAttendMeetingDetailsAuditDto>(AttendMeetingDetailsType) : null;
            result.EmployeeQualificationDetails = input.RelevantActivityDetailInformations != null ? input.RelevantActivityDetailInformations.ToList().GetEachTypeInformationDetail<EconomicSubstanceEmployeeQualificationDetailsAuditDto>(EmployeeQualificationDetailsType) : null;




            result.PremisesAddress = Mapper.Map<List<AddressAuditDto>>(input.PremisesAddress);
            // get the country of the Premises Address
            result.PremisesAddress.ForEach(x => { x.CountryText = _countryDict != null && x.CountryId != Guid.Empty ? _countryDict[x.CountryId].CountryName : null; });
            // holding part
            result.DoesEntityComplyItsStatutoryObligations = input.DoesEntityComplyItsStatutoryObligations;
            result.DoesEntityManageEquity = input.DoesEntityManageEquity;
            result.DoesEntityHaveAdequateEmployee = input.DoesEntityHaveAdequateEmployee;

            // intelect
            result.IsLegalEntityHighRisk = input.IsLegalEntityHighRisk;
            result.DoesEntityProvideEvidence = input.DoesEntityProvideEvidence;
            result.TotalGrossAnnualIncome = input.TotalGrossAnnualIncome;
            result.GrossIncomeRoyalities = input.GrossIncomeRoyalities;
            result.GrossIncomeGains = input.GrossIncomeGains;
            result.GrossIncomeOthers = input.GrossIncomeOthers;
            result.DoesLegalEntityConductCIGA = input.DoesLegalEntityConductCIGA;
            result.DoesEntityProvideEvidenceroRebut = input.DoesEntityProvideEvidenceroRebut;
            result.DoesBusinessRequireEquipment = input.DoesBusinessRequireEquipment;
            result.EquipmentInJurisdiction = input.EquipmentInJurisdiction;
            result.EquipmentDescription = input.EquipmentDescription;

            if (input.RelevantActivitiesDocuments != null)
            {
                var activeDocuments = input.RelevantActivitiesDocuments.Where(x => !x.IsDeleted).ToList();
                var documents = Mapper.Map<List<DocumentAuditDto>>(activeDocuments);

                result.HighRiskDocuments = documents
                    .Where(x => x.DocumentCategory.Equals(HighRiskDocuments, StringComparison.OrdinalIgnoreCase))
                    .ToList();

                result.OtherCIGADocuments = documents
                    .Where(x => x.DocumentCategory.Equals(OtherCIGADocuments, StringComparison.OrdinalIgnoreCase))
                    .ToList();

                // CR 2.0
                result.TangibleAssetIncomeDocuments = documents
                    .Where(x => x.DocumentCategory.Equals(TangibleAssetIncomeDocuments, StringComparison.OrdinalIgnoreCase))
                    .ToList();

                result.TangibleAssetEmployeeResponsibilityDocuments = documents
                   .Where(x => x.DocumentCategory.Equals(TangibleAssetEmployeeResponsibilityDocuments, StringComparison.OrdinalIgnoreCase))
                   .ToList();

                result.HistoryofStrategicDecisionsInBVIDocuments = documents
                   .Where(x => x.DocumentCategory.Equals(HistoryofStrategicDecisionsInBVIDocuments, StringComparison.OrdinalIgnoreCase))
                   .ToList();

                result.HistoryofTradingActivityIncomeDocuments = documents
                   .Where(x => x.DocumentCategory.Equals(HistoryofTradingActivityIncomeDocuments, StringComparison.OrdinalIgnoreCase))
                   .ToList();

                result.IPAssetsInBVIDocuments = documents
                   .Where(x => x.DocumentCategory.Equals(IPAssetsInBVIDocuments, StringComparison.OrdinalIgnoreCase))
                   .ToList();

                result.IPAssetsEmployeeResponsibilityDocuments = documents
                   .Where(x => x.DocumentCategory.Equals(IPAssetsEmployeeResponsibilityDocuments, StringComparison.OrdinalIgnoreCase))
                   .ToList();

                result.ConcreteEvidenceDecisionInBVIDocuments = documents
                   .Where(x => x.DocumentCategory.Equals(ConcreteEvidenceDecisionInBVIDocuments, StringComparison.OrdinalIgnoreCase))
                   .ToList();
            }


            // CR 2.0            
            result.GrossIncomeType = input.GrossIncomeType;
            result.TotalAssetsValue = input.TotalAssetsValue;
            result.NetAssetsValue = input.NetAssetsValue;
            result.NoofQuorumBoardMeeting = input.NoofQuorumBoardMeeting;
            result.IsQuorumBoardMeetingInBVI = input.IsQuorumBoardMeetingInBVI;
            result.TotalNoCorporateLegalEmployee = input.TotalNoCorporateLegalEmployee;
            result.TangibleAsset = input.TangibleAsset;
            result.TangibleAssetIncome = input.TangibleAssetIncome;
            result.TangibleAssetEmployeeResponsibility = input.TangibleAssetEmployeeResponsibility;
            result.HistoryofStrategicDecisionsInBVI = input.HistoryofStrategicDecisionsInBVI;
            result.HistoryofTradingActivityIncome = input.HistoryofTradingActivityIncome;
            result.RelevantIPAsset = input.RelevantIPAsset;
            result.IPAssetsInBVI = input.IPAssetsInBVI;
            result.IPAssetsEmployeeResponsibility = input.IPAssetsEmployeeResponsibility;
            result.ConcreteEvidenceDecisionInBVI = input.ConcreteEvidenceDecisionInBVI;

            return result;
        }

        public static EconomicSubstanceAuditDto MapToEconomicSubstanceAuditDto(this EconomicSubstanceDeclaration input)
        {
            try
            {
                string jurisdictionTaxResidentText = string.Empty;
                if (_countryDict != null && input.JurisdictionTaxResidentId.HasValue)
                {
                    _countryDict.TryGetValue(input.JurisdictionTaxResidentId.Value, out var country);
                    jurisdictionTaxResidentText = country?.CountryName ?? string.Empty;
                }
                string parentJurisdictionText = string.Empty;
                if (_countryDict != null && input.ParentJurisdictionId.HasValue)
                {
                    if (_countryDict.TryGetValue(input.ParentJurisdictionId.Value, out var country))
                    {
                        parentJurisdictionText = country.CountryName;
                    }
                }
                string currencyText = string.Empty;
                if (_currencyDict != null && input.CurrencyId.HasValue)
                {
                    if (_currencyDict.TryGetValue(input.CurrencyId.Value, out var currency))
                    {
                        currencyText = currency.CurrencyName;
                    }
                }
                EconomicSubstanceAuditDto result = new EconomicSubstanceAuditDto();
                result.Id = input.Id;
                result.CorporateEntityId = input.CorporateEntityId;
                result.IsFinaincialCanChange = input.IsFinaincialCanChange;
                result.IsDeleted = input.IsDeleted;
                result.FiscalStartDate = input.FiscalStartDate;
                result.FiscalEndDate = input.FiscalEndDate;
                result.DoesEntityMakeClaimOutisedBVI = input.DoesEntityMakeClaimOutisedBVI;
                result.JurisdictionTaxResidentId = input.JurisdictionTaxResidentId;
                result.JurisdictionTaxResidentText = jurisdictionTaxResidentText;
                result.DoesEntityHaveParentEntity = input.DoesEntityHaveParentEntity;
                result.ParentEntityName = input.ParentEntityName;
                result.ParentEntityAlternativeName = input.ParentEntityAlternativeName;
                result.ParentJurisdictionId = input.ParentJurisdictionId;
                result.ParentJurisdictionText = parentJurisdictionText;
                result.IsEvidenceNonResidenceOrTreatment = input.IsEvidenceNonResidenceOrTreatment;
                result.SupportingComments = input.SupportingComments;
                result.CurrencyId = input.CurrencyId;
                result.CurrencyText = currencyText;
                result.CurrencyExchangeRate = input.CurrencyExchangeRate;
                result.Status = input.Status.GetDescription();
                result.SubmissionDate = input.SubmissionDate;
                result.SubmitterName = input.SubmitterName;
                result.EntityIncorporationNumber = input.EntityIncorporationNumber;

                // Entity Details
                result.EsEntityDetails = input.EsEntityDetails?.Where(x => !x.IsDeleted).Select(x => { return x.MapToEsEntityDetailDto(); }).ToList();

                // Tax Residency 2.0
                result.TaxPayerIdentificationNumber = input.TaxPayerIdentificationNumber;
                               


                if (input.EconomicSubstanceDocuments != null)
                {
                    var activeDocuments = input.EconomicSubstanceDocuments.Where(x => !x.IsDeleted).ToList();
                    var documents = Mapper.Map<List<DocumentAuditDto>>(activeDocuments);

                    result.EvidenceOfNonResidanceDocument = documents
                        .Where(x => x.DocumentCategory.Equals(EvidenceNonResidency, StringComparison.OrdinalIgnoreCase))
                        .ToList();

                    result.EvidenceProvisionalTreatmentDocuments = documents
                        .Where(x => x.DocumentCategory.Equals(EvidenceProvisionalTreatment, StringComparison.OrdinalIgnoreCase))
                        .ToList();

                    result.SupportingDocument = documents
                        .Where(x => x.DocumentCategory.Equals(SupportingDocument, StringComparison.OrdinalIgnoreCase))
                        .ToList();
                }

                if (result.RelevantActivities == null) result.RelevantActivities = new List<EconomicSubstanceRelevantActivityAuditDto>();
                // need to map relevant activity
                var checkActivity = input.RelevantActivities.Where(x => x.IsChecked).ToList();
                if (checkActivity != null)
                {
                    foreach (var item in checkActivity)
                    {
                        var item1 = item.MapToRelevantActivityDetailAuditDto();
                        result.RelevantActivities.Add(item1);
                    }
                }
                
                return result;
            }
            catch(Exception ex)
            {
                LogException(ex);
                // Not sure why we are ignoring this issue.
                return null;
            }
        }




        public static List<EconomicSubstanceDeclarationDto> MapToEconomicSubstanceReportDtoList(this List<EconomicSubstanceDeclaration> input, bool incldeDetail = true)
        {
            List<EconomicSubstanceDeclarationDto> result = new List<EconomicSubstanceDeclarationDto>();
            input.ForEach(x => { result.Add(x.MapToEconomicSubstanceReportDto(incldeDetail)); });
            return result;
        }

        /// <summary>
        /// Map from EconomicSubstanceDeclaration to EconomicSubstanceDeclarationDto
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public static EconomicSubstanceDeclarationDto MapToEconomicSubstanceReportDto(this EconomicSubstanceDeclaration input, bool includeDetail = true)
        {
            EconomicSubstanceDeclarationDto result = new EconomicSubstanceDeclarationDto();
            result.Id = input.Id;
            result.IsDeleted = input.IsDeleted;
            result.CorporateEntityId = input.CorporateEntityId;
            result.CorporateEntity = _corporateEntity;
            result.IsFinaincialCanChange = input.IsFinaincialCanChange;
            result.FiscalStartDate = input.FiscalStartDate;
            result.FiscalEndDate = input.FiscalEndDate;


            result.FiscalStartDateString = input.FiscalStartDate.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
            result.FiscalEndDateString = input.FiscalEndDate.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);



            result.AnyRelevantActivtyDuringFiscalYear = input.AnyRelevantActivtyDuringFiscalYear;
            result.EvidenceOfNonResidanceDocument = input.EconomicSubstanceDocuments != null ? Mapper.Map<List<EvidenceNonResidencyDocumentsDto>>(input.EconomicSubstanceDocuments.Where(x => x.DocumentCategory == EvidenceNonResidency && !x.IsDeleted).ToList()) : null;
            result.EvidenceProvisionalTreatmentDocuments = input.EconomicSubstanceDocuments != null ? Mapper.Map<List<EvidenceProvisionalTreatmentDocumentsDto>>(input.EconomicSubstanceDocuments.Where(x => x.DocumentCategory == EvidenceProvisionalTreatment && !x.IsDeleted).ToList()) : null;
            result.SupportingDocument = input.EconomicSubstanceDocuments != null ? Mapper.Map<List<SupportingDocumentDto>>(input.EconomicSubstanceDocuments.Where(x => x.DocumentCategory == SupportingDocument && !x.IsDeleted).ToList()) : null;
            result.DoesEntityMakeClaimOutisedBVI = input.DoesEntityMakeClaimOutisedBVI;
            result.JurisdictionTaxResidentId = input.JurisdictionTaxResidentId;
            result.JurisdictionTaxResident = _countryDict?.ContainsKey(result.JurisdictionTaxResidentId ?? Guid.Empty) == true ? _countryDict[result.JurisdictionTaxResidentId ?? Guid.Empty] : null;
            result.DoesEntityHaveParentEntity = input.DoesEntityHaveParentEntity;
            result.ParentEntityName = input.ParentEntityName;
            result.ParentEntityAlternativeName = input.ParentEntityAlternativeName;
            result.ParentJurisdictionId = input.ParentJurisdictionId;
            result.EntityIncorporationNumber = input.EntityIncorporationNumber;
            result.ParentJurisdiction = _countryDict?.ContainsKey(result.ParentJurisdictionId ?? Guid.Empty) == true ? _countryDict[result.ParentJurisdictionId ?? Guid.Empty] : null;
            result.IsEvidenceNonResidenceOrTreatment = input.IsEvidenceNonResidenceOrTreatment;
            result.SupportingComments = input.SupportingComments;
            result.Status = input.Status;
            result.SubmissionDate = input.SubmissionDate;
            result.SubmitterName = input.SubmitterName;

            result.CurrencyId = input.CurrencyId;
            result.Currency = _currencyDict?.ContainsKey(result.CurrencyId ?? Guid.Empty) == true ? _currencyDict[result.CurrencyId ?? Guid.Empty] : null;

            result.CurrencyExchangeRate = input.CurrencyExchangeRate;

            if (includeDetail)
            {
                if (result.RelevantActivities == null)
                    result.RelevantActivities = new List<RelevantActivityDetailDto>();

                result.RelevantActivities = input.RelevantActivities != null ? input.RelevantActivities.Where(x => x.IsChecked && !x.IsDeleted).Select(x => { return x.MapToRelevantActivityDetailReportDto(); }).ToList() : null;
                result.RelevantActivities = AddMissingRelevantActivity(result.RelevantActivities);
            }

            // provisional treatment 

            result.ApproveRejectProvisionalTreatment = input.ApproveRejectProvisionalTreatment;
            result.PrTreatmentDueDateString = input.ProvisionalTreatmentDueDate?.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);

            result.ProvisionalTreatmentDueDate = input.ProvisionalTreatmentDueDate;
            result.ProvisionalTreatmentDocument = input.EconomicSubstanceDocuments != null ? Mapper.Map<List<ProvisionalTreatmentDocumentDto>>(input.EconomicSubstanceDocuments.Where(x => x.DocumentCategory == ProvisionalTreatmentDocument && !x.IsDeleted).ToList()) : null;
            result.ProvisionalTreatmentSubmittedBy = input.ProvisionalTreatmentSubmittedBy;
            result.ProvisionalTreatmentSubmissionDate = input.ProvisionalTreatmentSubmissionDate;

            //
            // Information Request 

            result.EconomicSubstanceInformationRequired = input.EconomicSubstanceInformationRequired?.ToList().MapToInformationRequirementDto();

            return result;
        }

        public static RelevantActivityDetailDto MapToRelevantActivityDetailReportDto(this RelevantActivityDetail input)
        {
            RelevantActivityDetailDto result = new RelevantActivityDetailDto();
            result.Id = input.Id;
            result.EconomicSubstanceDeclarationId = input.EconomicSubstanceDeclarationId;
            result.RelevantActivityName = input.RelevantActivityName;
            result.ReleventActivityValue = input.ReleventActivityValue;
            result.IsChecked = input.IsChecked;
            result.IsCarriedForPartFinancialPeriod = input.IsCarriedForPartFinancialPeriod;
            result.IsGrossIncomeEarned = input.IsGrossIncomeEarned;
            result.StartDate = input.StartDate;
            result.EndDate = input.EndDate;

            result.StartDateString = input.StartDate.HasValue ? input.StartDate.Value.ToString("dd/MM/yyyy") : string.Empty;
            result.EndDateString = input.EndDate.HasValue ? input.EndDate.Value.ToString("dd/MM/yyyy") : string.Empty;

            result.IsActivityDirectedInBVI = input.IsActivityDirectedInBVI;
            result.NoofConductedMeeting = input.NoofConductedMeeting;
            result.NoofMeetingHeldInBVI = input.NoofMeetingHeldInBVI;
            result.IsMeetingMinutesInBVI = input.IsMeetingMinutesInBVI;
            result.TotalTurnover = input.TotalTurnover;
            result.TotalExpediture = input.TotalExpediture;
            result.TotalExpeditureInBVI = input.TotalExpeditureInBVI;
            result.TotalNoFullTimeEmployee = input.TotalNoFullTimeEmployee;
            result.TotalFullTimeEmployeeInBVI = input.TotalFullTimeEmployeeInBVI;
            result.CIGAOtherDetail = input.CIGAOtherDetail;
            result.HasAnyIncomeBeenOutsourced = input.HasAnyIncomeBeenOutsourced;
            result.WasCIGAOutsourcedInBVI = input.WasCIGAOutsourcedInBVI;
            result.TotalExpenditureIncurredInBVI = input.TotalExpenditureIncurredInBVI;
            //result.ConductedCIGAActivity = Mapper.Map<List<CIGARelevantActivityDto>>(input.ConductedCIGAActivity.Where(x => !x.IsDeleted));
            //result.ConductedCIGAActivity = Mapper.Map<List<CIGARelevantActivityDto>>(input.ConductedCIGAActivity);

            //if (result.ConductedCIGAActivity != null)
            //{
            //    result.ConductedCIGAActivity.ForEach(x => x.CIGAActivity = x.CIGAActivityId != Guid.Empty && _cigaDict.ContainsKey(x.CIGAActivityId) ? _cigaDict[x.CIGAActivityId] : null);
            //    result.ConductedCIGAActivity = result.ConductedCIGAActivity.Where(x => x.CIGAActivity != null).ToList();
            //}

            result.ServiceProviders = input.RelevantActivityDetailInformations != null ? input.RelevantActivityDetailInformations.ToList().GetEachTypeInformationDetail<ServiceProvidersDto>(ServiceProvidersType) : null;
            result.ManagementDetails = input.RelevantActivityDetailInformations != null ? input.RelevantActivityDetailInformations.ToList().GetEachTypeInformationDetail<PersonDetailsDto>(ManagementDetailsType) : null;
            result.AttendMeetingDetails = input.RelevantActivityDetailInformations != null ? input.RelevantActivityDetailInformations.ToList().GetEachTypeInformationDetail<AttendMeetingDetailsDto>(AttendMeetingDetailsType) : null;
            result.EmployeeQualificationDetails = input.RelevantActivityDetailInformations != null ? input.RelevantActivityDetailInformations.ToList().GetEachTypeInformationDetail<EmployeeQualificationDetailsDto>(EmployeeQualificationDetailsType) : null;
            result.PremisesAddress = Mapper.Map<List<AddressDto>>(input.PremisesAddress);
            // get the country of the Premises Address
            result.PremisesAddress.ForEach(x => { x.Country = _countryDict != null && _countryDict.ContainsKey(x.CountryId) ? _countryDict[x.CountryId] : null; });
            // holding part
            result.DoesEntityComplyItsStatutoryObligations = input.DoesEntityComplyItsStatutoryObligations;
            result.DoesEntityManageEquity = input.DoesEntityManageEquity;
            result.DoesEntityHaveAdequateEmployee = input.DoesEntityHaveAdequateEmployee;

            // intelect
            result.IsLegalEntityHighRisk = input.IsLegalEntityHighRisk;
            result.DoesEntityProvideEvidence = input.DoesEntityProvideEvidence;
            result.TotalGrossAnnualIncome = input.TotalGrossAnnualIncome;
            result.GrossIncomeRoyalities = input.GrossIncomeRoyalities;
            result.GrossIncomeGains = input.GrossIncomeGains;
            result.GrossIncomeOthers = input.GrossIncomeOthers;
            result.DoesLegalEntityConductCIGA = input.DoesLegalEntityConductCIGA;
            result.DoesEntityProvideEvidenceroRebut = input.DoesEntityProvideEvidenceroRebut;
            result.DoesBusinessRequireEquipment = input.DoesBusinessRequireEquipment;
            result.EquipmentInJurisdiction = input.EquipmentInJurisdiction;
            result.EquipmentDescription = input.EquipmentDescription;

            // CR 2.0           
            result.GrossIncomeType = input.GrossIncomeType;
            result.TotalAssetsValue = input.TotalAssetsValue;
            result.NetAssetsValue = input.NetAssetsValue;
            result.NoofQuorumBoardMeeting = input.NoofQuorumBoardMeeting;
            result.IsQuorumBoardMeetingInBVI = input.IsQuorumBoardMeetingInBVI;
            result.TotalNoCorporateLegalEmployee = input.TotalNoCorporateLegalEmployee;
            result.TangibleAsset = input.TangibleAsset;
            result.TangibleAssetIncome = input.TangibleAssetIncome;
            result.TangibleAssetEmployeeResponsibility = input.TangibleAssetEmployeeResponsibility;
            result.HistoryofStrategicDecisionsInBVI = input.HistoryofStrategicDecisionsInBVI;
            result.HistoryofTradingActivityIncome = input.HistoryofTradingActivityIncome;

            result.RelevantIPAsset = input.RelevantIPAsset;
            result.IPAssetsInBVI = input.IPAssetsInBVI;
            result.IPAssetsEmployeeResponsibility = input.IPAssetsEmployeeResponsibility;
            result.ConcreteEvidenceDecisionInBVI = input.ConcreteEvidenceDecisionInBVI;

            return result;
        }

        public static EsEntityDetailDto MapToEsEntityDetailDto(this EsEntityDetail input)
        {
            EsEntityDetailDto result = new EsEntityDetailDto();
            result.Id = input.Id;
            result.IsActive = input.IsActive;
            result.IsDeleted = input.IsDeleted;
            result.CreatedAt = input.CreatedAt;
            result.CreatedBy = input.CreatedBy;
            result.UpdatedAt = input.UpdatedAt;
            result.UpdatedBy = input.UpdatedBy;
            result.EconomicSubstanceDeclarationId = input.EconomicSubstanceDeclarationId;
            result.CorporateEntityId = input.CorporateEntityId;
            result.IdentificationNumber = input.IdentificationNumber;
            result.TotalGrossAnnualIncome = input.TotalGrossAnnualIncome;
            result.IsSameAsRegisteredAddress = input.IsSameAsRegisteredAddress;
            result.AddressLine1 = input.AddressLine1;
            result.AddressLine2 = input.AddressLine2;
            result.CountryId = input.CountryId;
            result.Country = _countryDict?.ContainsKey(result.CountryId ?? Guid.Empty) == true ? _countryDict[result.CountryId ?? Guid.Empty] : null;
            result.CountryText = result.Country?.CountryName;
            
            result.MNEGroupName = input.MNEGroupName;

            result.DoesEntityHaveUltimateParent = input.DoesEntityHaveUltimateParent;           
            result.DoesEntityHaveImmediateParent = input.DoesEntityHaveImmediateParent;

            // Entity Additional Details
            result.EsEntityAdditionalDetails = input.EsEntityAdditionalDetails?.Where(x => !x.IsDeleted).Select(x => { return x.MapToEsEntityAdditionalDetailDto(); }).ToList();

            return result;
        }

        public static EsEntityAdditionalDetailDto MapToEsEntityAdditionalDetailDto(this EsEntityAdditionalDetail input)
        {
            EsEntityAdditionalDetailDto result = new EsEntityAdditionalDetailDto();
            result.Id = input.Id;
            result.IsActive = input.IsActive;
            result.IsDeleted = input.IsDeleted;
            result.CreatedAt = input.CreatedAt;
            result.CreatedBy = input.CreatedBy;
            result.UpdatedAt = input.UpdatedAt;
            result.UpdatedBy = input.UpdatedBy;            

            ParentEntityType parentEntity;
            if (Enum.TryParse<ParentEntityType>(input.EntityType, true, out parentEntity))
            {
                result.EntityType = Enum.Parse<ParentEntityType>(input.EntityType);
            }
            else
            {
                result.EntityType = null;
            }

            result.Name = input.Name;
            result.AlternativeName = input.AlternativeName;
            result.IncorporationNumber = input.IncorporationNumber;
            result.IdentificationNumber = input.IdentificationNumber;
            result.JurisdictionId = input.JurisdictionId;
            result.Jurisdiction = _countryDict?.ContainsKey(result.JurisdictionId ?? Guid.Empty) == true ? _countryDict[result.JurisdictionId ?? Guid.Empty] : null;
            result.JurisdictionText = result.Jurisdiction?.CountryName;

            return result;
        }

    }
}
