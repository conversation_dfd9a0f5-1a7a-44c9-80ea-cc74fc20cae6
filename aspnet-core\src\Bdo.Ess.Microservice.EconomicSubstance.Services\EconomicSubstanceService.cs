using AutoMapper;
using Bdo.Ess.Common;
using Bdo.Ess.Common.Audit;
using Bdo.Ess.Common.EconomicSubstance;
using Bdo.Ess.Common.Enums;
using Bdo.Ess.Common.Extensions;
using Bdo.Ess.Common.Helpers;
using Bdo.Ess.Common.Search;
using Bdo.Ess.Dtos.Audit.EconomicSubstance;
using Bdo.Ess.Dtos.CA.Dashboard;
using Bdo.Ess.Dtos.Common;
using Bdo.Ess.Dtos.EconomicSubstance;
using Bdo.Ess.Dtos.FileUpload.Validate;
using Bdo.Ess.Dtos.Integration;
using Bdo.Ess.Dtos.LookUp;
using Bdo.Ess.Dtos.MasterData;
using Bdo.Ess.Dtos.Workflow;
using Bdo.Ess.Microservice.Core.EntityFramework.Ctsp;
using Bdo.Ess.Microservice.Core.EntityFramework.Ctsp.Objects;
using Bdo.Ess.Microservice.Core.Services;
using Bdo.Ess.Microservice.Core.Utilities;
using Bdo.Ess.Microservice.EconomicSubstance.EntityFramework;
using Bdo.Ess.Microservice.EconomicSubstance.EntityFramework.Objects;
using Bdo.Ess.Services.EconomicSubstance;
using Bdo.Ess.Services.EconomicSubstanceSearch;
using Bdo.Ess.Services.InformationExchange;
using Bdo.Ess.Services.LookUp;
using Bdo.Ess.Services.MasterData;
using Bdo.Ess.Services.RedFlags;
using Bdo.Ess.Services.Workflow;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Reactive.Linq;
using System.Reactive.Threading.Tasks;
using System.Threading.Tasks;
using AddressDto = Bdo.Ess.Dtos.MasterData.AddressDto;

namespace Bdo.Ess.Microservice.EconomicSubstance.Services
{
    public class EconomicSubstanceService : ServiceBase<EconomicSubstanceDbContext, IEconomicSubstanceMicroservice>, IEconomicSubstanceMicroservice
    {
        private const string ESDefaultName = "Not Started";
        private const string USDCurrency = "USD";


        private static IMapper Mapper = new MapperConfiguration(cfg =>
        {
            cfg.CreateMap<AddressDto, Address>();
            cfg.CreateMap<Address, AddressDto>();

            cfg.CreateMap<Documents, DocumentsDto>();
            cfg.CreateMap<DocumentsDto, Documents>();
            cfg.CreateMap<DocumentInputDto, Documents>();
            cfg.CreateMap<Documents, DocumentOutputDto>();
            cfg.CreateMap<RelevantActivityDetailDto, RelevantActivityDetail>();
            cfg.CreateMap<RelevantActivityDetail, RelevantActivityDetailDto>();

            cfg.CreateMap<CIGARelevantActivity, CIGARelevantActivityDto>();
            cfg.CreateMap<CIGARelevantActivityDto, CIGARelevantActivity>();

            cfg.CreateMap<EconomicSubstanceDeclarationDto, EconomicSubstanceDeclaration>();
            cfg.CreateMap<EconomicSubstanceDeclaration, EconomicSubstanceDeclarationDto>();
            cfg.CreateMap<EconomicSubstanceDeclarationHistory, EconomicSubstanceDeclarationHistoryDto>();

            cfg.CreateMap<EntityDtoBase, ObjectBase>();
            cfg.CreateMap<ObjectBase, EntityDtoBase>();

            cfg.CreateMap<EsInformationRequestDocumentCaDto, EsInformationRequestDocumentCa>()
            .ReverseMap();
           
        }).CreateMapper();

        private readonly MicroserviceClient<ILookupMainMicroservice> _lookupMainMicroservice;
        private readonly EconomicSubstanceAuditor _economicSubstanceAuditor;
        private readonly EconomicSubstanceEventAuditor _economicSubstanceEventAuditor;

        private readonly EconomicSubstanceEventRequestedInformationAuditor _economicSubstanceEventRequestedInformationAuditor;
        private readonly EconomicSubstanceEventProvisionalTreatmentAuditor _economicSubstanceEventProvisionalTreatmentAuditor;

        private readonly MicroserviceClient<ICorporateEntityMicroservice> _corporateEntityMicroservice;
        private readonly MicroserviceClient<IWorkflowMicroservice> _workflowMicroservice;
        private readonly MicroserviceClient<ICaWorkflowMicroservice> _caWorkflowService;

        private readonly MicroserviceClient<IInformationExchangeMicroservice> _informationExchangeMicroservice;
        private readonly MicroserviceClient<IRedFlagExecutionMicroservice> _redFlagMicroservice;


        private readonly MicroserviceClient<IEconomicSubstanceSearchMicroservice> _economicSubstanceSearchMicroservice;

        private readonly ICurrencyExchangeAPI _currencyExchangeAPI;
        private readonly string ctspNumber = "";

        public EconomicSubstanceService(
            UnitOfWork<EconomicSubstanceDbContext> unitOfWork,
            EssSession essSession,
            MicroserviceClient<ILookupMainMicroservice> lookupMainMicroservice,
            EconomicSubstanceAuditor economicSubstanceAuditor,
            EconomicSubstanceEventAuditor economicSubstanceEventAuditor,
            EconomicSubstanceEventRequestedInformationAuditor economicSubstanceEventRequestedInformationAuditor,
            EconomicSubstanceEventProvisionalTreatmentAuditor economicSubstanceEventProvisionalTreatmentAuditor,
        MicroserviceClient<ICorporateEntityMicroservice> corporateEntityMicroservice,
            MicroserviceClient<IWorkflowMicroservice> workflowMicroservice,
            MicroserviceClient<ICaWorkflowMicroservice> caWorkflowService,
            MicroserviceClient<IRedFlagExecutionMicroservice> redFlagMicroservice,
            MicroserviceClient<IInformationExchangeMicroservice> informationExchangeMicroservice,
            MicroserviceClient<IEconomicSubstanceSearchMicroservice> economicSubstanceSearchMicroservice,
            ICurrencyExchangeAPI currencyExchangeAPI,
            ILogger<EconomicSubstanceService> logger

            ) : base(unitOfWork, essSession, logger)
        {
            _lookupMainMicroservice = lookupMainMicroservice;
            _economicSubstanceAuditor = economicSubstanceAuditor;
            _economicSubstanceEventAuditor = economicSubstanceEventAuditor;
            _economicSubstanceEventRequestedInformationAuditor = economicSubstanceEventRequestedInformationAuditor;
            _economicSubstanceEventProvisionalTreatmentAuditor = economicSubstanceEventProvisionalTreatmentAuditor;
            _corporateEntityMicroservice = corporateEntityMicroservice;
            _workflowMicroservice = workflowMicroservice;
            _currencyExchangeAPI = currencyExchangeAPI;
            _informationExchangeMicroservice = informationExchangeMicroservice;
            _caWorkflowService = caWorkflowService;
            _redFlagMicroservice = redFlagMicroservice;
            _economicSubstanceSearchMicroservice = economicSubstanceSearchMicroservice;
            ctspNumber = GetSessionValue<string>(EssSessionKeys.CtspNumber);
        }

        public async Task<bool> AddAddress(AddressDto input)
        {
            var repo = GetRepository<Address>();
            Address address = Mapper.Map<Address>(input);
            repo.Add(address);
            await CommitAsync();
            return true;
        }


        public async Task<DocumentOutputDto> UploadDocuments(DocumentInputDto input)
        {
            Logger.LogDebug($"UploadDocuments: {input.FileName} ({input.ContetntType}) content: {(input.Content.Length < 10 ? input.Content : $"length {input.Content.Length.ToString()}")}");

            var repo = GetRepository<Documents>();
            Documents doc = new Documents();
            doc.FileName = input.FileName;
            doc.ContetntType = input.ContetntType;
            doc.Content = Convert.FromBase64String(input.Content);
            repo.Add(doc);
            await CommitAsync();

            Logger.LogDebug($"Document {doc.Id} created!");

            return Mapper.Map<DocumentOutputDto>(doc);
        }

        public async Task DeleteOrphanDocuments(NoInput value)
        {
            try
            {
                var repo = GetRepository<Documents>();
                await repo.QueryFromSQL("sp_Delete_Orphan_Documents").ToListAsync();

            }
            catch (Exception ex)
            {
                Logger.LogDebug($"UploadDocuments cleaning fail  {ex.Message}");
            }
        }


        public async Task<DocumentsDto> GetDocument(DocumentOutputDto input)
        {
            var repo = GetRepository<Documents>();
            var resultDoc = await repo.Query(x => x.Id == input.Id).FirstOrDefaultAsync();
            return Mapper.Map<DocumentsDto>(resultDoc);
        }

        public async Task<List<DocumentsDto>> GetDocumentInformation(SingleFieldInput<List<Guid>> input)
        {
            var repo = GetRepository<Documents>();
            var resultDocs = await repo.Query(x => input.Value.Contains(x.Id)).ToListAsync();

            return resultDocs?.Select(x => new DocumentsDto { ContetntType = x.ContetntType, FileName = x.FileName, Id = x.Id }).ToList();
        }



        public async Task<bool> RemoveDocument(DocumentOutputDto input)
        {
            var repo = GetRepository<Documents>();
            var resultDoc = await repo.Query(x => x.Id == input.Id).FirstOrDefaultAsync();
            resultDoc.IsDeleted = true;
            await CommitAsync();


            return true;
        }

        /// <summary>
        /// Send the Economic substance declaration ID return true if the object get deleted remove the entity and all its dependency
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>


        private bool AddToPendingTransaction<T>(IDictionary<Type, IList<object>> pendingTransactions, Type key, T value)
        {
            if (pendingTransactions == null)
                return false;

            if (pendingTransactions.ContainsKey(key))
            {
                pendingTransactions[key].Add(value);
            }
            else
            {
                pendingTransactions.Add(key, new List<object>());
                pendingTransactions[key].Add(value);
            }
            return true;
        }

        private void HardDelete<T>(Dictionary<Type, IList<Object>> DeletedDictionary)
        {
            var repoAdd = GetRepository<T>();
            IList<object> items;
            DeletedDictionary.TryGetValue(typeof(T), out items);
            if (items != null) foreach (var item in items) repoAdd.HardDelete((T)item);

        }


        private async Task<bool> HardDeleteESS(Guid input)
        {
            Dictionary<Type, IList<Object>> DeletedDictionary = new Dictionary<Type, IList<Object>>();
            try
            {
                var repo = GetRepository<EconomicSubstanceDeclaration>();
                var repo1 = GetRepository<RelevantActivityDetail>();
                var result = await repo.Query().Where(x => x.Id == input).Include(x => x.EconomicSubstanceDocuments).ThenInclude(x => x.Documents).FirstOrDefaultAsync();
                var result_sub = await repo1.Query(x => x.EconomicSubstanceDeclarationId == input).Include(x => x.RelevantActivityDetailInformations).Include(x => x.PremisesAddress).Include(x => x.ConductedCIGAActivity).Include(x => x.RelevantActivitiesDocuments).ThenInclude(x => x.Documents).ToListAsync();
                result.RelevantActivities = result_sub;


                AddToPendingTransaction(DeletedDictionary, typeof(EconomicSubstanceDeclaration), result);

                // get the Documents objects both Econoomic and Relevent activities
                var economicDocuments = result.EconomicSubstanceDocuments.Select(x => x.Documents).ToList();
                economicDocuments.ForEach(x => AddToPendingTransaction(DeletedDictionary, typeof(Documents), x));
                var relevantActivityDocuments = result.RelevantActivities.SelectMany(x => x.RelevantActivitiesDocuments).ToList();
                relevantActivityDocuments.ForEach(x => AddToPendingTransaction(DeletedDictionary, typeof(Documents), x.Documents));

                // relevant activity documents 
                relevantActivityDocuments.ForEach(x => AddToPendingTransaction(DeletedDictionary, typeof(RelevantActivitiesDocuments), x));

                // the economic substance doduments 
                result.EconomicSubstanceDocuments.ToList().ForEach(x => AddToPendingTransaction(DeletedDictionary, typeof(EconomicSubstanceDocuments), x));


                // relevant activity Information Table
                var relevantActivtyInformation = result.RelevantActivities.SelectMany(x => x.RelevantActivityDetailInformations).ToList();
                relevantActivtyInformation.ForEach(x => AddToPendingTransaction(DeletedDictionary, typeof(RelevantActivityDetailInformations), x));


                // get the CIGA Relevant Activity
                var cigaRelevantActivity = result.RelevantActivities.SelectMany(x => x.ConductedCIGAActivity).ToList();
                cigaRelevantActivity.ForEach(x => AddToPendingTransaction(DeletedDictionary, typeof(CIGARelevantActivity), x));

                // get the Address details 

                var primisesAddress = result.RelevantActivities.SelectMany(x => x.PremisesAddress).ToList();
                primisesAddress.ForEach(x => AddToPendingTransaction(DeletedDictionary, typeof(Address), x));

                // add all relevant activities
                var relevantActivities = result.RelevantActivities.Select(x => x).ToList();
                relevantActivities.ForEach(x => AddToPendingTransaction(DeletedDictionary, typeof(RelevantActivityDetail), x));



                HardDelete<Address>(DeletedDictionary);
                HardDelete<Documents>(DeletedDictionary);
                HardDelete<CIGARelevantActivity>(DeletedDictionary);
                HardDelete<RelevantActivityDetailInformations>(DeletedDictionary);
                HardDelete<RelevantActivitiesDocuments>(DeletedDictionary);
                HardDelete<RelevantActivityDetail>(DeletedDictionary);
                HardDelete<EconomicSubstanceDocuments>(DeletedDictionary);
                HardDelete<EconomicSubstanceDeclaration>(DeletedDictionary);

                await CommitAsync();

                await _economicSubstanceAuditor.AuditDelete(result);

            }
            catch
            {
                return false;
            }
            return true;
        }

        // send the CoperationEntity ID
        public async Task<bool> PermanentDelete(SingleFieldInput<Guid> input)
        {
            bool result = true;
            var repo = GetRepository<EconomicSubstanceDeclaration>();
            /// get all the ESS ID 
            /// 
            var essDclartionUndeleted = await repo.Query(x => x.CorporateEntityId == input.Value && !x.IsDeleted).ToArrayAsync();
            if (essDclartionUndeleted.Any()) return false;

            var essDeclarations = await repo.Query(x => x.CorporateEntityId == input.Value && x.IsDeleted).ToArrayAsync();

            for (int i = 0; i < essDeclarations.Count(); i++) result = await HardDeleteESS(essDeclarations[i].Id);

            return result;
        }
        // need also to update the dto of the documents
        public async Task<bool> RemoveEconomicSubstanceDocuments(SingleFieldInput<Guid> input)
        {
            var repo = GetRepository<EconomicSubstanceDocuments>();
            var resultDoc = await repo.Query(x => x.Id == input.Value).FirstOrDefaultAsync();
            if (resultDoc != null)
            {
                resultDoc.IsDeleted = true;

                repo.Update(resultDoc);
                await CommitAsync();
                return true;
            }
            return false;
        }

        public async Task<bool> RemoveRelativeActivitiesDocuments(SingleFieldInput<Guid> input)
        {
            var repo = GetRepository<RelevantActivitiesDocuments>();
            var resultDoc = await repo.Query(x => x.Id == input.Value).FirstOrDefaultAsync();
            if (resultDoc != null)
            {
                resultDoc.IsDeleted = true;

                repo.Update(resultDoc);
                await CommitAsync();
                return true;
            }
            return false;
        }


        private void PopulateImportData(EconomicSubstanceDeclarationDto input)
        {
            input.JurisdictionTaxResidentId = input.JurisdictionTaxResident != null ? input.JurisdictionTaxResident.Id : input.JurisdictionTaxResidentId;
            input.ParentJurisdictionId = input.ParentJurisdiction != null ? input.ParentJurisdiction.Id : input.ParentJurisdictionId;
            input.CurrencyId = input.Currency != null ? input.Currency.Id : input.CurrencyId;

            if (input.RelevantActivities != null)
            {
                var seletedActivities = input.RelevantActivities.Where(x => x.IsChecked).ToList();
                foreach (var active in seletedActivities)
                {
                    active.NoofConductedMeeting = !string.IsNullOrEmpty(active.NoofConductedMeetingString) ? active.NoofConductedMeetingString.ToInt() : null;
                    active.NoofMeetingHeldInBVI = !string.IsNullOrEmpty(active.NoofMeetingHeldInBVIString) ? active.NoofMeetingHeldInBVIString.ToInt() : null;
                    active.TotalTurnover = !string.IsNullOrEmpty(active.TotalTurnoverString) ? active.TotalTurnoverString.ToDecimal() : null;
                    active.TotalExpediture = !string.IsNullOrEmpty(active.TotalExpeditureString) ? active.TotalExpeditureString.ToDecimal() : null;
                    active.TotalExpeditureInBVI = !string.IsNullOrEmpty(active.TotalExpeditureInBVIString) ? active.TotalExpeditureInBVIString.ToDecimal() : null;
                    active.TotalNoFullTimeEmployee = !string.IsNullOrEmpty(active.TotalNoFullTimeEmployeeString) ? active.TotalNoFullTimeEmployeeString.ToDecimal() : null;
                    active.TotalFullTimeEmployeeInBVI = !string.IsNullOrEmpty(active.TotalFullTimeEmployeeInBVIString) ? active.TotalFullTimeEmployeeInBVIString.ToDecimal() : null;
                    active.TotalExpenditureIncurredInBVI = !string.IsNullOrEmpty(active.TotalExpenditureIncurredInBVIString) ? active.TotalExpenditureIncurredInBVIString.ToDecimal() : null;
                    active.TotalGrossAnnualIncome = !string.IsNullOrEmpty(active.TotalGrossAnnualIncomeString) ? active.TotalGrossAnnualIncomeString.ToDecimal() : null;
                    active.GrossIncomeRoyalities = !string.IsNullOrEmpty(active.GrossIncomeRoyalitiesString) ? active.GrossIncomeRoyalitiesString.ToDecimal() : null;
                    active.GrossIncomeGains = !string.IsNullOrEmpty(active.GrossIncomeGainsString) ? active.GrossIncomeGainsString.ToDecimal() : null;
                    active.GrossIncomeOthers = !string.IsNullOrEmpty(active.GrossIncomeOthersString) ? active.GrossIncomeOthersString.ToDecimal() : null;
                    active.ServiceProviders?.ForEach(item =>
                    {
                        item.HoursPerMonthForEmployee = !string.IsNullOrEmpty(item.HoursPerMonthForEmployeeString) ? item.HoursPerMonthForEmployeeString.ToDecimal() : null;
                        item.NumberOfStaffEmployed = !string.IsNullOrEmpty(item.NumberOfStaffEmployedString) ? item.NumberOfStaffEmployedString.ToDecimal() : null;
                    });

                    active.AttendMeetingDetails?.ForEach(item =>
                    {
                        item.MeetingNo = !string.IsNullOrEmpty(item.MeetingNoString) ? item.MeetingNoString.ToInt() : null;
                    }
                           );
                    active.EmployeeQualificationDetails?.ForEach(item =>
                    {
                        item.YearRelevantExperience = !string.IsNullOrEmpty(item.YearRelevantExperienceString) ? item.YearRelevantExperienceString.ToDecimal() : null;
                    });

                    active.NetAssetsValue = !string.IsNullOrEmpty(active.NetAssetsValueString) ? active.NetAssetsValueString.ToDecimal() : null;
                    active.NoofQuorumBoardMeeting = !string.IsNullOrEmpty(active.NoofQuorumBoardMeetingString) ? active.NoofQuorumBoardMeetingString.ToInt() : null;
                    active.IsQuorumBoardMeetingInBVI = !string.IsNullOrEmpty(active.IsQuorumBoardMeetingInBVIString) ? active.IsQuorumBoardMeetingInBVIString.ToBoolean() : null;
                    active.TotalNoCorporateLegalEmployee = !string.IsNullOrEmpty(active.TotalNoCorporateLegalEmployeeString) ? active.TotalNoCorporateLegalEmployeeString.ToDecimal() : null;
                }
            }

            var entityDetails = input.EsEntityDetails?.Where(x => !x.IsDeleted).ToList();
            foreach (var entity in entityDetails)
            {
                entity.CorporateEntityId = input.CorporateEntityId;
                entity.TotalGrossAnnualIncome = !string.IsNullOrEmpty(entity.TotalGrossAnnualIncomeString) ? entity.TotalGrossAnnualIncomeString.ToDecimal() : null;
                entity.IsSameAsRegisteredAddress = !string.IsNullOrEmpty(entity.IsSameAsRegisteredAddressString) ? entity.IsSameAsRegisteredAddressString.ToBoolean() : null;
                if (entity.IsSameAsRegisteredAddress.HasValue && entity.IsSameAsRegisteredAddress.Value)
                {
                    var address = _informationExchangeMicroservice.Call(x => x.GetEntityAddressFromBoss(new GetEntityInput { Id = input.CorporateEntity.ExternalReferenceId })).Result;

                    entity.AddressLine1 = address?.AddressLine1 ?? entity.AddressLine1;
                    entity.AddressLine2 = address?.AddressLine2 ?? entity.AddressLine2;
                    entity.CountryId = input.CorporateEntity?.CountryId != Guid.Empty ? input.CorporateEntity?.CountryId : entity.CountryId;
                }
                else
                {
                    entity.CountryId = entity.Country != null && entity.Country.Id != Guid.Empty ? entity.Country.Id : entity.CountryId;
                }

                entity.DoesEntityHaveUltimateParent = !string.IsNullOrEmpty(entity.DoesEntityHaveUltimateParentString) ? entity.DoesEntityHaveUltimateParentString.ToBoolean() : null;
                entity.DoesEntityHaveImmediateParent = !string.IsNullOrEmpty(entity.DoesEntityHaveImmediateParentString) ? entity.DoesEntityHaveImmediateParentString.ToBoolean() : null;

                foreach (var additionalEntityDetail in entity.EsEntityAdditionalDetails)
                {
                    additionalEntityDetail.JurisdictionId = additionalEntityDetail.Jurisdiction != null && additionalEntityDetail.Jurisdiction.Id != Guid.Empty ? additionalEntityDetail.Jurisdiction.Id : additionalEntityDetail.JurisdictionId;
                }
            }
        }

        void ClearOldData(EconomicSubstanceDeclarationDto currentEconomicSubstance)
        {
            if (!currentEconomicSubstance.DoesEntityMakeClaimOutisedBVI.HasValue || (currentEconomicSubstance.DoesEntityMakeClaimOutisedBVI.HasValue && !currentEconomicSubstance.DoesEntityMakeClaimOutisedBVI.Value))
            {
                currentEconomicSubstance.JurisdictionTaxResident = null;
                currentEconomicSubstance.JurisdictionTaxResidentId = null;
                currentEconomicSubstance.DoesEntityHaveParentEntity = null;
                currentEconomicSubstance.ParentEntityName = null;
                currentEconomicSubstance.ParentEntityAlternativeName = null;
                currentEconomicSubstance.ParentJurisdiction = null;
                currentEconomicSubstance.ParentJurisdictionId = null;
                currentEconomicSubstance.EntityIncorporationNumber = null;
                currentEconomicSubstance.IsEvidenceNonResidenceOrTreatment = null;
                currentEconomicSubstance.TaxPayerIdentificationNumber = null;

                if (currentEconomicSubstance.EvidenceOfNonResidanceDocument != null)
                {
                    currentEconomicSubstance.EvidenceOfNonResidanceDocument = null;
                }
                if (currentEconomicSubstance.EvidenceProvisionalTreatmentDocuments != null)
                {
                    currentEconomicSubstance.EvidenceProvisionalTreatmentDocuments = null;
                }
            }
            else
            {
                if (!currentEconomicSubstance.DoesEntityHaveParentEntity.HasValue || (currentEconomicSubstance.DoesEntityHaveParentEntity.HasValue && !currentEconomicSubstance.DoesEntityHaveParentEntity.Value))
                {
                    currentEconomicSubstance.ParentEntityName = null;
                    currentEconomicSubstance.ParentEntityAlternativeName = null;
                    currentEconomicSubstance.ParentJurisdiction = null;
                    currentEconomicSubstance.ParentJurisdictionId = null;
                    currentEconomicSubstance.EntityIncorporationNumber = null;
                }
            }
            var activities = currentEconomicSubstance.RelevantActivities.Where(x => x.IsChecked).ToList();
            foreach (var x in activities)
            {
                if (!x.IsCarriedForPartFinancialPeriod.HasValue || (x.IsCarriedForPartFinancialPeriod.HasValue && !x.IsCarriedForPartFinancialPeriod.Value))
                { x.StartDateString = null; x.EndDateString = null; x.StartDate = null; x.EndDate = null; }
                if (currentEconomicSubstance.DoesEntityMakeClaimOutisedBVI.HasValue && currentEconomicSubstance.DoesEntityMakeClaimOutisedBVI.Value)
                {
                    ClearActivityProperties(x);
                }

                if (x.ReleventActivityValue == RelevantActivity.None && x.IsChecked)
                {
                    x.PremisesAddress?.Clear();
                    x.ConductedCIGAActivity?.Clear();
                    x.ServiceProviders?.Clear();
                    x.ManagementDetails?.Clear();
                    x.AttendMeetingDetails?.Clear();
                    x.EmployeeQualificationDetails?.Clear();
                }
            }
        }

        private void ClearActivityProperties(RelevantActivityDetailDto x)
        {
            x.IsActivityDirectedInBVI = null;
            x.NoofConductedMeetingString = null;
            x.NoofMeetingHeldInBVIString = null;

            x.IsMeetingMinutesInBVI = null;
            x.TotalTurnoverString = null;

            x.TotalExpeditureString = null;
            x.TotalExpeditureInBVIString = null;
            x.TotalNoFullTimeEmployeeString = null;
            x.TotalFullTimeEmployeeInBVIString = null;

            x.CIGAOtherDetail = null;
            x.HasAnyIncomeBeenOutsourced = null;
            x.WasCIGAOutsourcedInBVI = null;
            x.TotalExpenditureIncurredInBVI = null;


            x.DoesEntityComplyItsStatutoryObligations = null;
            x.DoesEntityManageEquity = null;
            x.DoesEntityHaveAdequateEmployee = null;

            // intelect
            x.IsLegalEntityHighRisk = null;
            x.DoesEntityProvideEvidence = null;
            x.TotalExpenditureIncurredInBVIString = null;
            x.GrossIncomeRoyalitiesString = null;

            x.TotalGrossAnnualIncomeString = null;
            x.GrossIncomeGainsString = null;
            x.GrossIncomeOthersString = null;
            x.DoesLegalEntityConductCIGA = null;
            x.DoesEntityProvideEvidenceroRebut = null;
            x.DoesBusinessRequireEquipment = null;
            x.EquipmentInJurisdiction = null;
            x.EquipmentDescription = null;

            x.ConductedCIGAActivity = null;
            x.PremisesAddress = null;
            x.ServiceProviders = null;
            x.ManagementDetails = null;
            x.AttendMeetingDetails = null;
            x.EmployeeQualificationDetails = null;
            x.OtherCIGADocuments = null;
            x.HighRiskDocuments = null;
            x.TangibleAssetIncomeDocuments = null;
            x.TangibleAssetEmployeeResponsibilityDocuments = null;
            x.HistoryofStrategicDecisionsInBVIDocuments = null;
            x.HistoryofTradingActivityIncomeDocuments = null;
            x.IPAssetsInBVIDocuments = null;
            x.IPAssetsEmployeeResponsibilityDocuments = null;
            x.ConcreteEvidenceDecisionInBVIDocuments = null;

            //BVI Declaration Changes 2.0
            x.GrossIncomeType = null;
            x.TotalAssetsValue = null;
            x.NetAssetsValue = null;
            x.NoofQuorumBoardMeeting = null;
            x.IsQuorumBoardMeetingInBVI = null;
            x.TotalNoCorporateLegalEmployee = null;
            x.TangibleAsset = null;

            x.TangibleAssetIncome = null;
            x.TangibleAssetIncomeDocuments = null;

            x.TangibleAssetEmployeeResponsibility = null;
            x.TangibleAssetEmployeeResponsibilityDocuments = null;

            x.HistoryofStrategicDecisionsInBVI = null;
            x.HistoryofStrategicDecisionsInBVIDocuments = null;

            x.HistoryofTradingActivityIncome = null;
            x.HistoryofTradingActivityIncomeDocuments = null;

            x.RelevantIPAsset = null;

            x.IPAssetsInBVI = null;
            x.IPAssetsInBVIDocuments = null;

            x.IPAssetsEmployeeResponsibility = null;
            x.IPAssetsEmployeeResponsibilityDocuments = null;

            x.ConcreteEvidenceDecisionInBVI = null;
            x.ConcreteEvidenceDecisionInBVIDocuments = null;
        }

        /// <summary>
        ///The function will check if we need to update the existing declaration or add a new declaration 
        ///also need to check the  start and end financial date for validation
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private Tuple<bool, bool, EconomicSubstanceDeclaration> CheckDeclarationUpdateBeoreImport(EconomicSubstanceDeclarationDto input)
        {

            if (!input.FiscalStartDate.IsDateNull() && !input.FiscalEndDate.IsDateNull())
            {
                var repo = GetRepository<EconomicSubstanceDeclaration>();

                var result = repo.Query(x => x.CorporateEntityId == input.CorporateEntityId).Include(x => x.RelevantActivities).ToList();
                var resulSameDec = result.FirstOrDefault(x => x.FiscalStartDate.Equals(input.FiscalStartDate) && x.FiscalEndDate.Equals(input.FiscalEndDate) && x.Status == EconomicSubstanceStatus.ReOpen);
                if (resulSameDec != null) return Tuple.Create(true, false, resulSameDec);

                var validationResult = ValidationHelper.IsVaildFiscalYear(input, result, "Invalid Financial period start date. You cannot create declarations with overlapping Financial periods.");
                return Tuple.Create(false, string.IsNullOrEmpty(validationResult.FieldName), resulSameDec);
            }
            return Tuple.Create(false, false, new EconomicSubstanceDeclaration());
        }

        private void MergeDeclaration(EconomicSubstanceDeclaration oldDeclaratio, EconomicSubstanceDeclaration newDeclaration)
        {
            oldDeclaratio.RelevantActivities?.ToList()?.ForEach(x => { x.IsDeleted = true; x.IsChecked = false; newDeclaration.RelevantActivities.Add(x); });
            newDeclaration.Id = oldDeclaratio.Id;

        }

        public async Task<EconomicSubstanceDeclarationDto> ImportEconomicSubstance(EconomicSubstanceDeclarationDto input)
        {
            using (Logger.BeginScope(new Dictionary<string, object>
                {
                    { "CspNumber", GetSessionValue<string>(EssSessionKeys.CtspNumber) },
                    { "EntityUniqueId",input?.CorporateEntity?.ExternalReferenceId},
                    { "CorporateEntityId", input.CorporateEntityId }
                }))
            {

                Logger.LogInformation("ImportEconomicSubstance starting CSP Number");

                ClearOldData(input);
                input = input.FixDateTime();
                PopulateImportData(input);
                var updateStatusAndValidation = CheckDeclarationUpdateBeoreImport(input);
                var userName = GetSessionValue<string>(EssSessionKeys.UserName);
                var repo = GetRepository<EconomicSubstanceDeclaration>();

                if (updateStatusAndValidation.Item1 || updateStatusAndValidation.Item2)
                {
                    await GetExchangeRate(input);
                    await GetlookUpTableValueFromMicroService(Guid.Empty);
                    var result = input.MapToEconomicSubstance();
                    result.UploadedDeclarationId = input.UploadedDeclarationId;
                    result.SubmitterName = userName;
                    if (updateStatusAndValidation.Item1)
                    {
                        result.Status = input.Status == EconomicSubstanceStatus.Draft ? EconomicSubstanceStatus.ReOpen : EconomicSubstanceStatus.ReSubmitted;
                        // need to mark all the old economic substance details marked as deleted before adding the new one
                        MergeDeclaration(updateStatusAndValidation.Item3, result);
                        repo.Update(result);
                    }
                    else
                    {
                        repo.Add(result);
                    }
                    await CommitAsync();

                    Logger.LogDebug("ImportEconomicSubstance after commit CSP Number");

                    var addedEs = await GetEconomicSubstanceDetailById(new GetEconomicSubstanceByEntityDetailIdDto { Id = result.Id });
                    if (input.Status == EconomicSubstanceStatus.Submitted || input.Status == EconomicSubstanceStatus.ReSubmitted)
                    {

                        Logger.LogInformation("Submitting declaration to workflow service");
                        await _workflowMicroservice.Call(x => x.AddESSubmittedForAssessment(addedEs));
                    }

                    Logger.LogDebug("ImportEconomicSubstance workflow finished");

                    return addedEs;
                }
                return null;
            }
        }


        public async Task<EconomicSubstanceDeclarationDto[]> ALLImportEconomicSubstance(EconomicSubstanceDeclarationDto[] input)
        {
            using (Logger.BeginScope(new Dictionary<string, object>
            {
                { "CspNumber", GetSessionValue<string>(EssSessionKeys.CtspNumber) }
            }))
            {
                var userName = GetSessionValue<string>(EssSessionKeys.UserName);
                var repo = GetRepository<EconomicSubstanceDeclaration>();
                await GetlookUpTableValueFromMicroService(Guid.Empty);
                List<EconomicSubstanceDeclaration> resultDS = new List<EconomicSubstanceDeclaration>();
                List<EconomicSubstanceDeclaration> resultUDS = new List<EconomicSubstanceDeclaration>();
                for (int i = 0; i < input.Count(); i++)
                {
                    ClearOldData(input[i]);
                    input[i] = input[i].FixDateTime();
                    PopulateImportData(input[i]);

                    // need to check if we need to add or update 
                    var updateStatusAndValidation = CheckDeclarationUpdateBeoreImport(input[i]);
                    if (updateStatusAndValidation.Item1 || updateStatusAndValidation.Item2)
                    {
                        await GetExchangeRate(input[i]);
                        var result = input[i].MapToEconomicSubstance();
                        result.UploadedDeclarationId = input[i].UploadedDeclarationId;
                        result.SubmitterName = userName;
                        if (updateStatusAndValidation.Item1)
                        {
                            result.Status = input[i].Status == EconomicSubstanceStatus.Draft ? EconomicSubstanceStatus.ReOpen : EconomicSubstanceStatus.ReSubmitted;
                            // need to mark all the old economic substance details marked as deleted before adding the new one
                            MergeDeclaration(updateStatusAndValidation.Item3, result);
                            resultUDS.Add(result);
                        }
                        else
                            resultDS.Add(result);
                    }
                }
                repo.AddRange(resultDS.ToArray());
                repo.UpdateRange(resultUDS.ToArray());
                Commit();

                // populate the UploadeID to return Dto
                var mergedresult = resultDS.Concat(resultUDS).ToList();
                var esdto = mergedresult.MapToEconomicSubstanceDtoList();

                foreach (var item in esdto)
                {
                    var itemFound = input.FirstOrDefault(x => x.CorporateEntityId == item.CorporateEntityId &&
                             x.FiscalStartDateString == item.FiscalStartDateString &&
                             x.FiscalEndDateString == item.FiscalEndDateString
                            );

                    if (itemFound != null) { item.UploadedDeclarationId = itemFound.UploadedDeclarationId; }
                }
                return esdto.ToArray();
            }
        }

        public async Task<EconomicSubstanceDeclarationDto> AddEconomicSubstance(EconomicSubstanceDeclarationDto input)
        {
            using (Logger.BeginScope(new Dictionary<string, object>
            {
                { "CspNumber", GetSessionValue<string>(EssSessionKeys.CtspNumber) },
                { "EntityUniqueId",input?.CorporateEntity?.ExternalReferenceId},
                { "CorporateEntityId", input.CorporateEntityId }
            }))
            {
                try
                {
                    // need to hack the date time
                    input = input.FixDateTime();
                    var userName = GetSessionValue<string>(EssSessionKeys.UserName);
                    var repo = GetRepository<EconomicSubstanceDeclaration>();

                    var result = input.MapToEconomicSubstance();
                    result.SubmitterName = userName;
                    repo.Add(result);
                    await CommitAsync();

                    await _economicSubstanceAuditor.AuditCreate(result);

                    return await GetEconomicSubstanceDetailById(new GetEconomicSubstanceByEntityDetailIdDto { Id = result.Id });
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "AddEconomicSubstance encountered an exception.");
                    throw;
                }

            }
        }

        public async Task<DeclarationNotificationInfoDto> GetNotifcationEntityDetail(SingleFieldInput<Guid> input)
        {
            DeclarationNotificationInfoDto output = new DeclarationNotificationInfoDto();
            var repo = GetRepository<EconomicSubstanceDeclaration>();

            var detail = await repo.Query(x => x.Id == input.Value).FirstOrDefaultAsync();
            if (detail != null)
            {
                output.FiscalEndDate = detail.FiscalEndDate;
                var entityId = new GetEntityInput { Id = detail.CorporateEntityId };
                var corporateEntity = await _corporateEntityMicroservice.Call(x => x.GetEntity(entityId));
                if (corporateEntity != null)
                {

                    output.Name = corporateEntity.Name;
                    output.IncorpFormationNo = corporateEntity.CompanyNumber.ConvertToNullIfEmpty() ?? corporateEntity.FormationNumber.ConvertToNullIfEmpty();
                }

            }
            return output;
        }

        /// <summary>
        /// update the ES if the ES is completed then add the ES to the Assessment table for review
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<EconomicSubstanceDeclarationDto> UpdateEconomicSubstance(EconomicSubstanceDeclarationDto input)
        {
            using (Logger.BeginScope(new Dictionary<string, object>
            {
                { "CspNumber", GetSessionValue<string>(EssSessionKeys.CtspNumber) },
                { "DeclarationId",input.Id },
                { "EntityUniqueId",input?.CorporateEntity?.ExternalReferenceId },
                { "CorporateEntityId", input.CorporateEntityId }
            }))
            {
                try
                {
                    // need to hack the date time

                    input = input.FixDateTime();
                    var userName = GetSessionValue<string>(EssSessionKeys.UserName);
                    input.SubmitterName = userName;
                    var repo = GetRepository<EconomicSubstanceDeclaration>();
                    // need to get the exchange Rate
                    await GetExchangeRate(input);

                    var result = input.MapToEconomicSubstance();
                    var previous = await GetEconomicSubstanceById(new GetEconomicSubstanceByEntityIdDto { Id = input.Id });
                    result.Synced = false;

                    repo.Update(result);
                    await CommitAsync();

                    ////If The ES-Completed then add it to the the Assessment Table and set the value of The ES review is set NotStarted
                    if (input.Status == EconomicSubstanceStatus.Submitted || input.Status == EconomicSubstanceStatus.ReSubmitted)
                    {

                        await _workflowMicroservice.Call(x => x.AddESSubmittedForAssessment(input));
                        await _economicSubstanceEventAuditor.AuditSubmit(result, previous.Status, input.Status == EconomicSubstanceStatus.Submitted ? AuditAction.Submit : AuditAction.Resubmit);
                    }

                    await _economicSubstanceAuditor.AuditUpdate(previous, result);


                    var updatedES = await GetEconomicSubstanceDetailById(new GetEconomicSubstanceByEntityDetailIdDto { Id = input.Id });
                    return updatedES;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "UpdateEconomicSubstance encountered an exception.");
                    throw;
                }
            }
        }
        private async Task<List<EconomicSubstanceDeclarationDto>> GetEconomicSubstanceByEntityIDInternal(Guid id, bool isCa)
        {
            // get the CE first
            var entityId = new GetEntityInput { Id = id };
            var CorporateEntity = await _corporateEntityMicroservice.Call(x => x.GetEntity(entityId));

            // get the ES 
            var repo = GetRepository<EconomicSubstanceDeclaration>();

            var repoHistroy = GetRepository<EconomicSubstanceDeclarationHistory>();


            var result = await repo.Query(x => x.CorporateEntityId == id && !x.IsDeleted).Decorate(x => isCa ? x.Where(y => y.Status == EconomicSubstanceStatus.Submitted || y.Status == EconomicSubstanceStatus.ReSubmitted) : x).ToListAsync();
            var esIds = result.Select(x => x.Id).ToList();

            var allHistory = await repoHistroy.Query(x => esIds.Contains(x.EconomicSubstanceDeclarationId)).Select(x => new { x.Id, x.EconomicSubstanceDeclarationId }).ToListAsync();
            var HistoryDic = allHistory.GroupBy(x => x.EconomicSubstanceDeclarationId).ToDictionary(g => g.Key, g => g);
            await GetlookUpTableValueFromMicroService(CorporateEntity.Id);
            var esdto = result.MapToEconomicSubstanceDtoList();

            // need to get the ES status from another service
            foreach (var item in esdto)
            {
                item.HasAnyDeclarationHistory = HistoryDic.ContainsKey(item.Id);
                if (isCa)
                {
                    var economicid = new SingleFieldInput<Guid> { Value = item.Id };
                    item.ESReviewStatus = await _workflowMicroservice.Call(x => x.GetESReviewStatusByESID(economicid));
                }
            }

            if (CorporateEntity.IsDeleted)
                return esdto.Where(x => !x.IsDeleted && x.Status == EconomicSubstanceStatus.Submitted).ToList();
            else
                return esdto.Where(x => !x.IsDeleted).ToList();

        }

        public async Task<List<EconomicSubstanceDeclarationDto>> GetEconomicSubstanceByEntityID(GetEconomicSubstanceByEntityIdDto input)
        {
            return await GetEconomicSubstanceByEntityIDInternal(input.Id, false);

        }

        public async Task<List<EconomicSubstanceDeclarationDto>> GetEconomicSubstanceDetailByIds(GetEntityIdForExport input)
        {
            using (Logger.BeginScope(new Dictionary<string, object>
                {
                    { "CspNumber", GetSessionValue<string>(EssSessionKeys.CtspNumber) },
                    { "FiscalYear", input.FinancialPeriodEndYear }
                }))
            {
                try
                {
                    Logger.LogInformation("GetEconomicSubstanceDetailByIds starting");
                    var declarationResult = await GetEconomicSubstanceByIdNew(input);
                    await GetlookUpTableValueFromMicroService(Guid.Empty);
                    var declarationList = declarationResult.MapToEconomicSubstanceDtoList();
                    Logger.LogInformation($"GetEconomicSubstanceDetailByIds Found {declarationList.Count} declarations");
                    return declarationList;
                }
                catch (Exception ex)
                {
                    Logger.LogDebug($"Exception in GetEconomicSubstanceDetailByIds: {ex.Message}");
                    Logger.LogDebug($"Stack trace:\n{ex.StackTrace}");
                    Logger.LogError(ex, "GetEconomicSubstanceDetailByIds encountered an exception.");
                    throw;
                }
            }
        }

        public async Task<List<EconomicSubstanceDeclarationDto>> GetESReportDetailByIds(GetEntityIdForExport input)
        {
            try
            {
                var economicSubstanceRepo = GetRepository<EconomicSubstanceDeclaration>();
                List<EconomicSubstanceDeclarationDto> declarationList = new List<EconomicSubstanceDeclarationDto>();
                GetEconomicSubstanceByEntityIdDto entityDto = new GetEconomicSubstanceByEntityIdDto();

                //var declarationResult = await economicSubstanceRepo.Query(y => input.Id.Contains(y.CorporateEntityId) && !y.IsDeleted 
                //          //  && y.FiscalEndDate.Year == input.FinancialPeriodEndYear
                //            ).ToArrayAsync();



                var declarationResult = await economicSubstanceRepo.Query(y => input.Id.Contains(y.CorporateEntityId) && !y.IsDeleted
                               //  && y.FiscalEndDate.Year == input.FinancialPeriodEndYear
                               ).Include(x => x.RelevantActivities)
                            .ToArrayAsync();


                declarationList = EconomicSubstanceMapping.MapToEconomicSubstanceReportDtoList(declarationResult.ToList());


                Logger.LogDebug($"Found {declarationList.Count} declarations");
                return declarationList;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Exception in GetESReportDetailByIds.");
                Logger.LogDebug($"Exception in GetESReportDetailByIds: {ex.Message}");
                Logger.LogDebug($"Stack trace:\n{ex.StackTrace}");
                throw;
            }
        }

        private async Task<EconomicSubstanceDeclaration> GetEconomicSubstanceById(GetEconomicSubstanceByEntityIdDto input)
        {
            // no need to include the document details
            var repo = GetRepository<EconomicSubstanceDeclaration>();
            var repo1 = GetRepository<RelevantActivityDetail>();
            var repo2 = GetRepository<EconomicSubstanceInformationRequired>();

            var result = await repo.Query(x => x.Id == input.Id)
                .Include(x => x.EconomicSubstanceDocuments)
                .Include(x => x.EsEntityDetails)
                .ThenInclude(x => x.EsEntityAdditionalDetails)
                .FirstOrDefaultAsync();
            var result_sub = await repo1
                .Query(x => x.EconomicSubstanceDeclarationId == input.Id && x.IsChecked)
                .Include(x => x.RelevantActivityDetailInformations)
                .Include(x => x.PremisesAddress)
                .Include(x => x.ConductedCIGAActivity)
                .Include(x => x.RelevantActivitiesDocuments)
                .ToListAsync();

            var resultRequiredInfo = await repo2
                                    .Query().Where(x => x.EconomicSubstanceDeclarationId == input.Id)
                                    .Include(x => x.EconomicSubstanceInformationRequiredDocuments)
                                    .Include(x => x.EsInformationRequestDocumentCas)
                                    .ToListAsync();

            result.RelevantActivities = result_sub;
            result.EconomicSubstanceInformationRequired = resultRequiredInfo;
            return result;
        }

        private async Task<List<EconomicSubstanceDeclaration>> GetEconomicSubstanceByIdNew(GetEntityIdForExport input)
        {
            var economicSubstanceRepo = GetRepository<EconomicSubstanceDeclaration>();

            var result = await economicSubstanceRepo.Query(y => input.Id.Contains(y.CorporateEntityId) && !y.IsDeleted && y.FiscalEndDate.Year == input.FinancialPeriodEndYear)
                        .Include(x => x.EconomicSubstanceDocuments)
                        .Include(x => x.EsEntityDetails)
                        .ThenInclude(x => x.EsEntityAdditionalDetails)
                        .Include(x => x.RelevantActivities)
                        .ThenInclude(x => x.RelevantActivityDetailInformations)
                        .Include(x => x.RelevantActivities)
                        .ThenInclude(x => x.PremisesAddress)
                        .Include(x => x.RelevantActivities)
                        .ThenInclude(x => x.ConductedCIGAActivity)
                        .Include(x => x.RelevantActivities)
                        .ThenInclude(x => x.RelevantActivitiesDocuments)
                        .Include(x => x.EconomicSubstanceInformationRequired)
                        .ThenInclude(x => x.EconomicSubstanceInformationRequiredDocuments)
                        .Include(x => x.EconomicSubstanceInformationRequired)
                        .ThenInclude(x => x.EsInformationRequestDocumentCas)
                        .ToListAsync();

            foreach (var item in result)
            {
                item.RelevantActivities = item.RelevantActivities.Where(x => x.IsChecked).ToList();
            }

            return result;
        }

        private async Task<bool> GetExchangeRate(EconomicSubstanceDeclarationDto input, bool needTorepopulate = true)
        {
            if ((input.Status == EconomicSubstanceStatus.Submitted || input.Status == EconomicSubstanceStatus.ReSubmitted)
               && input.Currency != null
               && !string.IsNullOrEmpty(input.Currency.CurrencyCode) && !string.IsNullOrEmpty(input.Currency.CurrencyName)
               && !input.Currency.CurrencyCode.Equals(USDCurrency, StringComparison.OrdinalIgnoreCase)
               && !input.FiscalEndDate.IsDateNull())
            {
                if (!input.CurrencyExchangeRate.HasValue || needTorepopulate)
                    input.CurrencyExchangeRate = await _currencyExchangeAPI.GetExchangeRate(input.Currency.CurrencyCode, input.FiscalEndDate.ToString("yyyy-MM-dd"));
                return input.CurrencyExchangeRate.HasValue;
            }
            return false;
        }


        public async Task<EconomicSubstanceDeclarationDto> GetEconomicSubstanceDetailById(GetEconomicSubstanceByEntityDetailIdDto input)
        {
            var result = await GetEconomicSubstanceById(new GetEconomicSubstanceByEntityIdDto { Id = input.Id });

            // need to check if the currency is set and the value of it is not a USD 
            // then need to get a new value and save it back to db before doing any other issue


            await GetlookUpTableValueFromMicroService(result.CorporateEntityId);
            var economicSubstancedto = result.MapToEconomicSubstanceDto();
            if (input.GetAllDetails)
            {
                GetDefaultFiscalYears(result.CorporateEntityId, economicSubstancedto);
            }
            return economicSubstancedto;
        }


        private async Task GetlookUpTableValueFromMicroService(Guid corporateEntityId)
        {
            //// get all the country once 

            var input = new SingleFieldInput<string>();
            var countries = await _lookupMainMicroservice.Call(x => x.GetAllCountriesIncludeNon(input));
            var countryDict = countries.ToDictionary(x => x.Id);


            input = new SingleFieldInput<string>();
            var currency = await _lookupMainMicroservice.Call(x => x.GetAllCurrencies(input));
            var currencyDict = currency.ToDictionary(x => x.Id);

            input = new SingleFieldInput<string>();
            var cigaLookup = await _lookupMainMicroservice.Call(x => x.GetCIGALookUps(input));
            var cigaLookupDict = cigaLookup.ToDictionary(x => x.Id);


            ////CorporateEntity 

            var entityId = new GetEntityInput { Id = corporateEntityId };
            var corporateEntity = corporateEntityId == Guid.Empty ? null : await _corporateEntityMicroservice.Call(x => x.GetEntity(entityId));

            EconomicSubstanceMapping.InitializeData(countryDict, cigaLookupDict, currencyDict, corporateEntity);

        }

        public async Task<bool> RemoveDraftEconomicSubstance(GetEconomicSubstanceByEntityIdDto input)
        {
            // mark the ES deleted on the server side insted of the client side

            var repo = GetRepository<EconomicSubstanceDeclaration>();
            var result = await repo.Query(x => x.Id == input.Id).SingleOrDefaultAsync();
            result.IsDeleted = true;
            repo.Update(result);

            await CommitAsync();

            await _economicSubstanceEventAuditor.AuditDiscard(result);

            return true;
        }

        private HashSet<string> _validCurrencyCodes;
        private HashSet<string> ValidCurrencyCodes
        {
            get
            {
                if (_validCurrencyCodes == null)
                {
                    var currencies = _lookupMainMicroservice.Call(x => x.GetAllCurrencies(new SingleFieldInput<string>())).Result;
                    _validCurrencyCodes = new HashSet<string>(currencies.Select(x => x.CurrencyCode.ToUpper()));
                }

                return _validCurrencyCodes;
            }
        }


        private HashSet<string> _validCountryCodes;
        private HashSet<string> ValidCountrycodes
        {
            get
            {
                if (_validCountryCodes == null)
                {
                    var countries = _lookupMainMicroservice.Call(x => x.GetAllCountries(NoInput.Input)).Result;
                    _validCountryCodes = new HashSet<string>(countries.Select(x => x.CountryCode.ToUpper()));
                }

                return _validCountryCodes;
            }
        }


        private HashSet<string> _validCigaCodes;
        private HashSet<string> ValidCigaCodes
        {
            get
            {
                if (_validCigaCodes == null)
                {
                    var cigas = _lookupMainMicroservice.Call(x => x.GeTCIGALookUp(new CIGAInputDto())).Result;
                    var filteredCigas = cigas.Where(x => !x.ActivityDetails.StartsWith("Other", StringComparison.OrdinalIgnoreCase)).ToList();
                    filteredCigas.Add(new CIGALookUpDto { AtivityEnumValue = 0, ActivityDetails = "Other", ActivityOrder = 1 });
                    _validCigaCodes = new HashSet<string>(filteredCigas.Select(x => $"{x.AtivityEnumValue}.{x.ActivityOrder}".ToUpper()));
                }

                return _validCigaCodes;
            }
        }



        public async Task<ListOfValidations[]> ValidateAllImportRules(ListEconomicSubstanceDeclarationDto[] economicSubstanceDeclarationDto)
        {

            List<ListOfValidations> listOfValidations = new List<ListOfValidations>();

            var countries = _lookupMainMicroservice.Call(x => x.GetAllCountries(NoInput.Input)).Result;
            var countryDict = countries.ToDictionary(x => x.CountryCode, x => x);

            var currencies = _lookupMainMicroservice.Call(x => x.GetAllCurrencies(new SingleFieldInput<string>())).Result;
            var curencyDict = currencies.ToDictionary(x => x.CurrencyCode, x => x);

            for (int i = 0; i < economicSubstanceDeclarationDto.Count(); i++)
            {

                ListOfValidations validation = new ListOfValidations();
                validation.ValidationResultDto = new List<ValidationResultDto>();
                VaildationEconomicInput input = new VaildationEconomicInput();
                input.countryDict = countryDict;
                input.curencyDict = curencyDict;
                input.economicSubstanceDeclarationDto = economicSubstanceDeclarationDto[i].economicSubstanceDeclarationDto;
                validation.DeclarationNo = economicSubstanceDeclarationDto[i].DeclarationNo;
                var result = await ValidationImportRules(input);
                validation.ValidationResultDto = result.SelectMany(x => x.ValidationResultDto).ToList();

                listOfValidations.Add(validation);
            }

            return listOfValidations.ToArray();
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>

        public async Task<List<ValidationResultMainDto>> ValidationImportRules(VaildationEconomicInput input)
        {
            using (Logger.BeginScope(new Dictionary<string, object>
                {
                    { "CspNumber", GetSessionValue<string>(EssSessionKeys.CtspNumber)},
                    { "EntityUniqueId",input?.economicSubstanceDeclarationDto?.CorporateEntity?.ExternalReferenceId}
                }))
            {


                try
                {

                    List<ValidationResultDto> resultvalidation = new List<ValidationResultDto>();

                    var validationResult = new List<ValidationResultMainDto>();

                    resultvalidation.Add(input.economicSubstanceDeclarationDto.IsFinaincialCanChangeString.IsBoolean(String.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, "6a."),
                        nameof(input.economicSubstanceDeclarationDto.IsFinaincialCanChange)));

                    resultvalidation.Add(input.economicSubstanceDeclarationDto.IsFinaincialCanChange.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "6a."),
                        nameof(input.economicSubstanceDeclarationDto.IsFinaincialCanChange)));

                    resultvalidation.Add(input.economicSubstanceDeclarationDto.FiscalStartDateString.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "6b."),
                        nameof(input.economicSubstanceDeclarationDto.FiscalStartDate)));
                    resultvalidation.Add(input.economicSubstanceDeclarationDto.FiscalStartDateString.IsDayMonthYear(string.Format(ValidationMessages.VALID_DATETIMEFORMAT_MESSAGE, "6b"),
                        nameof(input.economicSubstanceDeclarationDto.FiscalStartDate)));

                    resultvalidation.Add(input.economicSubstanceDeclarationDto.FiscalEndDateString.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "6c."),
                        nameof(input.economicSubstanceDeclarationDto.FiscalEndDate)));
                    resultvalidation.Add(input.economicSubstanceDeclarationDto.FiscalEndDateString.IsDayMonthYear(string.Format(ValidationMessages.VALID_DATETIMEFORMAT_MESSAGE, "6c"),
                        nameof(input.economicSubstanceDeclarationDto.FiscalEndDate)));

                    var count = resultvalidation.Where(x => x.FieldName == nameof(input.economicSubstanceDeclarationDto.IsFinaincialCanChange) || x.FieldName == nameof(input.economicSubstanceDeclarationDto.FiscalStartDate)
                                || x.FieldName == nameof(input.economicSubstanceDeclarationDto.FiscalEndDate)).ToList().Count;
                    if (count == 0)
                    {
                        input.economicSubstanceDeclarationDto.FiscalStartDate = input.economicSubstanceDeclarationDto.FiscalStartDateString.ConvertToDateTime();
                        input.economicSubstanceDeclarationDto.FiscalEndDate = input.economicSubstanceDeclarationDto.FiscalEndDateString.ConvertToDateTime();
                        // need to validate the rest if there is a value for the start and end  date
                        if (!input.economicSubstanceDeclarationDto.FiscalStartDate.IsDateNull() && !input.economicSubstanceDeclarationDto.FiscalEndDate.IsDateNull()
                            && input.economicSubstanceDeclarationDto.IsFinaincialCanChange.HasValue)
                        {
                            var repo = GetRepository<EconomicSubstanceDeclaration>();
                            input.economicSubstanceDeclarationDto.CorporateEntity =
                                 await _corporateEntityMicroservice.Call(x => x.GetEntity(new GetEntityInput { Id = input.economicSubstanceDeclarationDto.CorporateEntityId }));
                            var result = repo.Query(x => x.CorporateEntityId == input.economicSubstanceDeclarationDto.CorporateEntityId).ToList();

                            var isAnyDraftDeclaration = result.Any(x => x.Id != input.economicSubstanceDeclarationDto.Id && !x.IsDeleted && x.Status == EconomicSubstanceStatus.Draft);

                            if (isAnyDraftDeclaration)
                            {
                                resultvalidation.Add(new ValidationResultDto
                                {
                                    FieldName = nameof(input.economicSubstanceDeclarationDto.FiscalEndDate),
                                    ValidationString = "A draft declaration exist, please review before creating a new declaration",
                                    ValidationType = ValidationType.Warning
                                });
                            }

                            resultvalidation.Add(input.economicSubstanceDeclarationDto.IsPeriodStartsBeforeIncorporationDate("The financial period starts before the Date of Incorporation"));
                            resultvalidation.Add(input.economicSubstanceDeclarationDto.IsPeriodMoreThanAYear("Financial period cannot be longer than 12 months"));
                            resultvalidation.Add(input.economicSubstanceDeclarationDto.IsPeriodLessThanAYear("The financial period is less than 12 months"));

                            // need to different validation for a specific data 
                            var resulSameDec = result.FirstOrDefault(x => x.FiscalStartDate.Equals(input.economicSubstanceDeclarationDto.FiscalStartDate)
                                                                && x.FiscalEndDate.Equals(input.economicSubstanceDeclarationDto.FiscalEndDate)
                                                                && x.Status == EconomicSubstanceStatus.ReOpen);

                            if (resulSameDec == null)
                                resultvalidation.Add(ValidationHelper.IsVaildFiscalYear(input.economicSubstanceDeclarationDto, result, "Invalid Financial period start date. You cannot create declarations with overlapping Financial periods."));
                            else
                                resultvalidation.Add(new ValidationResultDto
                                {
                                    FieldName = "FiscalEndDateWarning",
                                    ValidationString = $"You have an existing declaration in Reopened status for this entity with financial period end date {resulSameDec.FiscalEndDate.ToString("dd/MM/yyyy")} Please confirm that you want to override the existing declarations since this process cannot be reversed."
                                    ,
                                    ValidationType = ValidationType.Warning
                                });

                            resultvalidation.Add(input.economicSubstanceDeclarationDto.IsStartDateGreaterThanEndDate("Financial end date must be greater than Financial start date"));
                            resultvalidation.Add(input.economicSubstanceDeclarationDto.FiscalEndDate.IsEndDateGraterThanCurrentDate("Financial end date must be less than the current date", nameof(input.economicSubstanceDeclarationDto.FiscalEndDate)));
                        }
                    }

                    resultvalidation = resultvalidation.Where(x => !string.IsNullOrEmpty(x.FieldName)).ToList();
                    validationResult.Add(new ValidationResultMainDto { ActivityName = "Step1", ValidationResultDto = new List<ValidationResultDto>(resultvalidation) });

                    resultvalidation.Clear();


                    //BVI Declaration Changes 2.0
                    if (input.economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                    {
                        if (!input.economicSubstanceDeclarationDto.EsEntityDetails.Any(x => !x.IsDeleted))
                        {
                            resultvalidation.Add(new ValidationResultDto { FieldName = nameof(input.economicSubstanceDeclarationDto.EsEntityDetails), ValidationString = "The user should add entity detail" });
                        }
                        else
                        {
                            foreach (var item in input.economicSubstanceDeclarationDto.EsEntityDetails.Where(x => !x.IsDeleted).ToList())
                            {
                                resultvalidation.AddRange(ValidateEntityDetails(item));
                            }
                        }
                    }

                    resultvalidation = resultvalidation.Where(x => !string.IsNullOrEmpty(x.FieldName)).ToList();

                    validationResult.Add(new ValidationResultMainDto { ActivityName = "EntityDetails", ValidationResultDto = new List<ValidationResultDto>(resultvalidation) });

                    resultvalidation.Clear();


                    if (input.economicSubstanceDeclarationDto.RelevantActivities.Where(x => x.IsChecked).ToList().Count == 0) resultvalidation.Add(new ValidationResultDto { ValidationString = "The user should select at least one relevant activity", FieldName = "RelevantActivities" });
                    else
                    {
                        List<ValidationResultDto> temp = new List<ValidationResultDto>();
                        foreach (var activity in input.economicSubstanceDeclarationDto.RelevantActivities.Where(x => x.IsChecked && x.RelevantActivityName != "None"))
                        {
                            resultvalidation.Add(activity.ReleventActivityValueString.IsValidActivity(String.Format(ValidationMessages.VALID_ACTIVITY_MESSAGE, "7b."), "RelevantActivities"));

                            resultvalidation.Add(activity.RelevantActivityName.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "7b."), "RelevantActivities"));

                            resultvalidation.Add(activity.IsCarriedForPartFinancialPeriodString.IsBoolean(String.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, "7a."), "RelevantActivities"));

                            if (activity.IsCarriedForPartFinancialPeriod.HasValue && activity.IsCarriedForPartFinancialPeriod.Value)
                            {
                                if (!string.IsNullOrEmpty(activity.StartDateString))
                                    temp.Add(activity.StartDateString.IsDayMonthYear(string.Format(ValidationMessages.VALID_DATETIMEFORMAT_MESSAGE, "7c"), "RelevantActivities"));
                                if (!string.IsNullOrEmpty(activity.EndDateString))
                                    temp.Add(activity.EndDateString.IsDayMonthYear(string.Format(ValidationMessages.VALID_DATETIMEFORMAT_MESSAGE, "7d"), "RelevantActivities"));
                            }
                            resultvalidation.AddRange(temp);
                            var count1 = temp.Count(x => !string.IsNullOrEmpty(x.FieldName));

                            if (activity.IsCarriedForPartFinancialPeriod.HasValue && activity.IsCarriedForPartFinancialPeriod.Value && count1 == 0)
                            {
                                RelevantActivityDetailDto relaventActivityDto = new RelevantActivityDetailDto();

                                if (!string.IsNullOrEmpty(activity.StartDateString))
                                    relaventActivityDto.StartDate = activity.StartDateString.ConvertToDateTime();
                                if (!string.IsNullOrEmpty(activity.EndDateString))
                                    relaventActivityDto.EndDate = activity.EndDateString.ConvertToDateTime();
                                resultvalidation.Add(relaventActivityDto.StartDate.IsNullOrEmpty(activity.ReleventActivityValue + "-Start Date is Empty", "RelevantActivities"));
                                resultvalidation.Add(relaventActivityDto.EndDate.IsNullOrEmpty(activity.ReleventActivityValue + "-End Date is Empty", "RelevantActivities"));
                                resultvalidation.Add(relaventActivityDto.IsStartDateGreaterThanEndDate(activity.ReleventActivityValue + "- End date must be greater than start date", "RelevantActivities"));
                                resultvalidation.Add(relaventActivityDto.IsDateWithinFiscalYear(input.economicSubstanceDeclarationDto.FiscalStartDate,
                                    input.economicSubstanceDeclarationDto.FiscalEndDate, activity.ReleventActivityValue + "-start date and end date must be within the Financial date range", "RelevantActivities"));

                            }
                            temp.Clear();
                        }
                    }
                    resultvalidation = resultvalidation.Where(x => !string.IsNullOrEmpty(x.FieldName)).ToList();

                    validationResult.Add(new ValidationResultMainDto { ActivityName = "Step2", ValidationResultDto = new List<ValidationResultDto>(resultvalidation) });

                    resultvalidation.Clear();

                    //// validate all of it 
                    ///
                    // no need to validate if the select activity is non

                    var isOnlyNone = input.economicSubstanceDeclarationDto.RelevantActivities.Count(x => x.RelevantActivityName == "None" && x.IsChecked);

                    if (isOnlyNone == 0)
                    {

                        resultvalidation.Add(input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVIString.IsBoolean(String.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, "8a."),
                            nameof(input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI)));

                        resultvalidation.Add(input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8a."),
                            nameof(input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI)));

                        if (input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI.HasValue && input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI.Value)
                        {

                            resultvalidation.Add(input.economicSubstanceDeclarationDto.JurisdictionTaxResidentString.IsCountryValid(input.countryDict,
                               String.Format(ValidationMessages.VALID_COUNTRYCODE_MESSAGE, "8b."),
                              nameof(input.economicSubstanceDeclarationDto.ParentJurisdiction), new List<PairMemberValue>()
                              { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));


                            resultvalidation.Add(input.economicSubstanceDeclarationDto.JurisdictionTaxResident.IsCountryNullOrNA(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8b."),
                                nameof(input.economicSubstanceDeclarationDto.JurisdictionTaxResident),
                                 new List<PairMemberValue>() { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));

                            if (!input.economicSubstanceDeclarationDto.JurisdictionTaxResident.IsCountryNull())
                                resultvalidation.Add(input.economicSubstanceDeclarationDto.JurisdictionTaxResident.CountryCode.IsValidCode(String.Format(ValidationMessages.VALID_COUNTRYCODE_MESSAGE, "8b."),
                                    nameof(input.economicSubstanceDeclarationDto.JurisdictionTaxResident), ValidCountrycodes, new List<PairMemberValue>()
                                    { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));


                            //BVI Declaration Changes 2.0
                            if (input.economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                            {
                                resultvalidation.Add(input.economicSubstanceDeclarationDto.TaxPayerIdentificationNumber.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8b.1"), nameof(input.economicSubstanceDeclarationDto.TaxPayerIdentificationNumber),
                                     new List<PairMemberValue>() { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));

                            }
                            else
                            {
                                resultvalidation.Add(input.economicSubstanceDeclarationDto.DoesEntityHaveParentEntityString.IsBoolean(String.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, "8c."),
                                    nameof(input.economicSubstanceDeclarationDto.DoesEntityHaveParentEntity),
                                    new List<PairMemberValue>() { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));

                                resultvalidation.Add(input.economicSubstanceDeclarationDto.DoesEntityHaveParentEntity.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8c."),
                                                        nameof(input.economicSubstanceDeclarationDto.DoesEntityHaveParentEntity),
                                        new List<PairMemberValue>() { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));

                                resultvalidation.Add(input.economicSubstanceDeclarationDto.ParentEntityName.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8c.1"),
                                                    nameof(input.economicSubstanceDeclarationDto.ParentEntityName),
                                                    new List<PairMemberValue>() { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityHaveParentEntity },
                                                                  new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));

                                resultvalidation.Add(input.economicSubstanceDeclarationDto.ParentEntityName.IsMaxLength(String.Format(ValidationMessages.VALID_LENGTH_MESSAGE, 100, "8c.1"),
                                     nameof(input.economicSubstanceDeclarationDto.ParentEntityName), 100, new List<PairMemberValue>()
                                     { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityHaveParentEntity },
                           new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));


                                resultvalidation.Add(input.economicSubstanceDeclarationDto.ParentEntityAlternativeName.IsMaxLength(String.Format(ValidationMessages.VALID_LENGTH_MESSAGE, 100, "8c.2"),
                                    nameof(input.economicSubstanceDeclarationDto.ParentEntityAlternativeName), 100, new List<PairMemberValue>()
                                    { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityHaveParentEntity },
                          new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));


                                resultvalidation.Add(input.economicSubstanceDeclarationDto.ParentJurisdiction.IsCountryNull(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8c.3"),
                                        nameof(input.economicSubstanceDeclarationDto.ParentJurisdiction), new List<PairMemberValue>()
                                        { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI },
                              new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityHaveParentEntity } }));


                                if (!input.economicSubstanceDeclarationDto.ParentJurisdiction.IsCountryNull())
                                    resultvalidation.Add(input.economicSubstanceDeclarationDto.ParentJurisdiction.CountryCode.IsValidCode(String.Format(ValidationMessages.VALID_COUNTRYCODE_MESSAGE, "8c.3"),
                                        nameof(input.economicSubstanceDeclarationDto.ParentJurisdiction), ValidCountrycodes, new List<PairMemberValue>()
                                        { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI },
                             new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityHaveParentEntity } }));


                                resultvalidation.Add(input.economicSubstanceDeclarationDto.EntityIncorporationNumber.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8c.4"),
                                        nameof(input.economicSubstanceDeclarationDto.EntityIncorporationNumber),
                                        new List<PairMemberValue>() {
                                new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityHaveParentEntity },
                                new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI }}));

                                resultvalidation.Add(input.economicSubstanceDeclarationDto.EntityIncorporationNumber.IsMaxLength(String.Format(ValidationMessages.VALID_TYPE_MESSAGE, "8c.4"),
                                    nameof(input.economicSubstanceDeclarationDto.ParentEntityAlternativeName), 20,
                                    new List<PairMemberValue>() { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityHaveParentEntity },
                                                      new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI }}));
                            }

                            resultvalidation.Add(input.economicSubstanceDeclarationDto.IsEvidenceNonResidenceOrTreatmentString.IsBoolean(String.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, "8d."),
                                  nameof(input.economicSubstanceDeclarationDto.IsEvidenceNonResidenceOrTreatment),
                                  new List<PairMemberValue>() { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));


                            resultvalidation.Add(input.economicSubstanceDeclarationDto.IsEvidenceNonResidenceOrTreatment.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8d."),
                                    nameof(input.economicSubstanceDeclarationDto.IsEvidenceNonResidenceOrTreatment),
                                    new List<PairMemberValue>() { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));

                            resultvalidation.Add(input.economicSubstanceDeclarationDto.EvidenceOfNonResidanceDocument.AnyUploadedDocument(String.Format(ValidationMessages.UPLOAD_ATTACHMENT_MESSAGE, "8d.1."), "ResidanceDocument",
                                new List<PairMemberValue>()
                                { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI },
                                      new PairMemberValue { Member = input.economicSubstanceDeclarationDto.IsEvidenceNonResidenceOrTreatment }
                                }));

                            resultvalidation.Add(input.economicSubstanceDeclarationDto.EvidenceProvisionalTreatmentDocuments.AnyUploadedDocument(String.Format(ValidationMessages.UPLOAD_ATTACHMENT_MESSAGE, "8d.2."), "ResidanceDocument",
                                new List<PairMemberValue>()
                                { new PairMemberValue { Member = input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI  },
                                     new PairMemberValue { Member = input.economicSubstanceDeclarationDto.IsEvidenceNonResidenceOrTreatment, Value=false }
                                }));
                        }
                    }
                    resultvalidation = resultvalidation.Where(x => !string.IsNullOrEmpty(x.FieldName)).Distinct().ToList();

                    validationResult.Add(new ValidationResultMainDto { ActivityName = "Step3", ValidationResultDto = new List<ValidationResultDto>(resultvalidation) });


                    if (input.economicSubstanceDeclarationDto.RelevantActivities.Count > 0 && input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI.HasValue &&
                        !input.economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI.Value)
                    {

                        if (input.economicSubstanceDeclarationDto.CurrencyString != null)
                        {
                            resultvalidation.Add(input.curencyDict.ContainsKey(input.economicSubstanceDeclarationDto.CurrencyString.Trim().ToUpper())
                               ? new ValidationResultDto
                               {
                                   FieldName = nameof(input.economicSubstanceDeclarationDto.CurrencyString),
                                   ValidationString = ValidationMessages.VALID_CURRENCYYCODE_MESSAGE
                               }
                               : new ValidationResultDto());
                        }

                        input.economicSubstanceDeclarationDto.RelevantActivities = input.economicSubstanceDeclarationDto.RelevantActivities.OrderBy(x => x.ReleventActivityValue).ToList();

                        char header = 'a';
                        foreach (var item in input.economicSubstanceDeclarationDto.RelevantActivities.Where(x => x.RelevantActivityName != "None" && x.IsChecked))
                        {
                            ValidationResultMainDto validationResultMainDto1 = new ValidationResultMainDto();

                            validationResultMainDto1.ActivityName = item.RelevantActivityName;
                            validationResultMainDto1.HeaderSeq = header;
                            int num = 0;
                            resultvalidation.Clear();

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 2, input.economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                //Turnover
                                //GrossIncome
                                num++;
                                resultvalidation.Add(item.TotalTurnoverString.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.TotalTurnover)));
                                resultvalidation.Add(item.TotalTurnoverString.IsDecimal(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"9{header}.{num}a."), nameof(item.TotalTurnover), 15, 2));

                                //BVI Declaration Changes 2.0
                                if (input.economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                {
                                    if (!item.IsLegalEntityHighRisk.HasValue || !item.IsLegalEntityHighRisk.Value)
                                    {
                                        resultvalidation.Add(item.GrossIncomeType.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}b."), nameof(item.GrossIncomeType)));
                                        resultvalidation.Add(item.GrossIncomeType.IsMaxLength(string.Format(ValidationMessages.VALID_LENGTH_MESSAGE, 255, $"9{header}.{num}b."), nameof(item.GrossIncomeType), 255));
                                    }

                                    if (item.ReleventActivityValue != RelevantActivity.HoldBA)
                                    {
                                        resultvalidation.Add(item.TotalAssetsValue.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}c."), nameof(item.TotalAssetsValue)));
                                        resultvalidation.Add(item.NetAssetsValueString.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}d."), nameof(item.NetAssetsValue)));
                                        resultvalidation.Add(item.NetAssetsValueString.IsDecimalIncludeNegetive(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"9{header}.{num}d."), nameof(item.NetAssetsValue), 15, 2));
                                    }
                                }
                            }
                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 1, input.economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                num++;
                                // Direction and Management validation
                                resultvalidation.Add(item.IsActivityDirectedInBVIString.IsBoolean(string.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, $"9{header}.{num}a."), nameof(item.IsActivityDirectedInBVI)));
                                resultvalidation.AddRange(ValidationHelper.ValidateUploadManagementDetail(item.ManagementDetails, $" For 9{header}.{num}b."));
                                resultvalidation.Add(item.NoofConductedMeetingString.IsInteger(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"9{header}.{num}c."), nameof(item.NoofConductedMeeting), 0, 999));
                                resultvalidation.Add(item.NoofMeetingHeldInBVIString.IsInteger(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"9{header}.{num}d."), nameof(item.NoofMeetingHeldInBVI), 0, 999));
                                resultvalidation.Add(item.IsMeetingMinutesInBVIString.IsBoolean(string.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, $"9{header}.{num}f."), nameof(item.IsMeetingMinutesInBVI)));

                                if (resultvalidation.Where(x => x.FieldName == nameof(item.NoofConductedMeeting) && x.FieldName == nameof(item.NoofMeetingHeldInBVI)).ToList().Count == 0)
                                {
                                    item.NoofConductedMeeting = item.NoofConductedMeetingString.ToInt();
                                    item.NoofMeetingHeldInBVI = item.NoofMeetingHeldInBVIString.ToInt();
                                    resultvalidation.Add(item.NoofConductedMeeting.ValueIsGreater(item.NoofMeetingHeldInBVI, string.Format(ValidationMessages.VALUE_EXCEED_MESSAGE, "# of Meetings", $"9{header}.{num}c."), nameof(item.NoofMeetingHeldInBVI)));
                                }

                                if (resultvalidation.Where(x => x.FieldName != nameof(item.NoofMeetingHeldInBVI)).ToList().Count == 0)
                                {
                                    resultvalidation.AddRange(ValidationHelper.ValidateAttendMeetingUploadDetail(item.AttendMeetingDetails, $" For 9{header}.{num}e."));
                                }

                                //BVI Declaration Changes 2.0
                                if (input.economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                {
                                    resultvalidation.Add(item.IsActivityDirectedInBVIString.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.IsActivityDirectedInBVI)));
                                    resultvalidation.Add(item.NoofConductedMeetingString.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}c."), nameof(item.NoofConductedMeeting)));
                                    resultvalidation.Add(item.NoofMeetingHeldInBVIString.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}d."), nameof(item.NoofMeetingHeldInBVI)));

                                    if (item.NoofMeetingHeldInBVI.HasValue && item.NoofMeetingHeldInBVI.Value > 0)
                                    {
                                        resultvalidation.AddRange(ValidationHelper.ValidateAttendMeetingUploadDetail(item.AttendMeetingDetails, $" For 9{header}.{num}e."));
                                        resultvalidation.Add(item.IsMeetingMinutesInBVIString.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}f."), nameof(item.IsMeetingMinutesInBVI)));
                                    }

                                    resultvalidation.Add(item.NoofQuorumBoardMeetingString.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}g."), nameof(item.NoofQuorumBoardMeeting)));
                                    resultvalidation.Add(item.NoofQuorumBoardMeetingString.IsInteger(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"9{header}.{num}g."), nameof(item.NoofQuorumBoardMeeting), 0, 999));
                                    if (resultvalidation.Where(x => x.FieldName == nameof(item.NoofQuorumBoardMeeting)).ToList().Count == 0)
                                    {
                                        item.NoofQuorumBoardMeeting = item.NoofQuorumBoardMeetingString.ToInt();
                                    }
                                    resultvalidation.Add(item.IsQuorumBoardMeetingInBVIString.IsBoolean(string.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, $"9{header}.{num}h."), nameof(item.IsQuorumBoardMeetingInBVI)));
                                    resultvalidation.Add(item.IsQuorumBoardMeetingInBVIString.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}h."), nameof(item.IsQuorumBoardMeetingInBVI)));
                                }
                            }
                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 3, input.economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                //Expenditure 
                                num++;
                                resultvalidation.Add(item.TotalExpeditureString.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.TotalExpediture)));
                                resultvalidation.Add(item.TotalExpeditureString.IsDecimal(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"9{header}.{num}a."), nameof(item.TotalExpediture), 15, 2));
                                resultvalidation.Add(item.TotalExpeditureInBVIString.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}b."), nameof(item.TotalExpeditureInBVI)));
                                resultvalidation.Add(item.TotalExpeditureInBVIString.IsDecimal(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"9{header}.{num}b."), nameof(item.TotalExpeditureInBVI), 15, 2));

                                if (resultvalidation.Where(x => x.FieldName == nameof(item.TotalExpediture) && x.FieldName == nameof(item.TotalExpeditureInBVI)).ToList().Count == 0)
                                {
                                    item.TotalExpediture = item.TotalExpeditureString.ToDecimal();
                                    item.TotalExpeditureInBVI = item.TotalExpeditureInBVIString.ToDecimal();
                                    resultvalidation.Add(item.TotalExpediture.ValueIsGreater(item.TotalExpeditureInBVI, string.Format(ValidationMessages.VALUE_EXCEED_MESSAGE, "Expediture", $"9{header}.{num}b."), nameof(item.TotalExpeditureInBVI)));
                                }

                            }

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 0, input.economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                num++;
                                //pure equity holding business
                                resultvalidation.Add(item.DoesEntityManageEquityString.IsBoolean(string.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, $"9{header}.{num}a."), nameof(item.DoesEntityManageEquity)));
                                resultvalidation.Add(item.DoesEntityComplyItsStatutoryObString.IsBoolean(string.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, "9{header}.{num}b."), nameof(item.DoesEntityComplyItsStatutoryObligations)));

                                //BVI Declaration Changes 2.0
                                if (input.economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                {
                                    resultvalidation.Add(item.DoesEntityManageEquityString.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.DoesEntityManageEquity)));
                                    if (item.DoesEntityManageEquity.HasValue && !item.DoesEntityManageEquity.Value)
                                    {
                                        resultvalidation.Add(item.DoesEntityComplyItsStatutoryObString.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}b."), nameof(item.DoesEntityComplyItsStatutoryObligations)));
                                    }
                                }
                                else
                                {
                                    //Old Declaration
                                    var validationWarrning = item.DoesEntityManageEquityString.IsNullOrEmpty($"Response is recommended for Question 9{header}.{num}a.", nameof(item.DoesEntityManageEquity));
                                    validationWarrning.ValidationType = ValidationType.Warning;
                                    resultvalidation.Add(validationWarrning);
                                }
                            }

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 4, input.economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                // employee
                                num++;
                                if (item.ReleventActivityValue != RelevantActivity.HoldBA || input.economicSubstanceDeclarationDto.FiscalEndDate.IsLessThan(EssConstants.DeclarationChanges_2_0_Date) || (item.DoesEntityManageEquity.HasValue && item.DoesEntityManageEquity.Value))
                                {
                                    resultvalidation.Add(item.TotalNoFullTimeEmployeeString.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.TotalNoFullTimeEmployee)));
                                    resultvalidation.Add(item.TotalNoFullTimeEmployeeString.IsDecimal(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"9{header}.{num}a."), nameof(item.TotalNoFullTimeEmployee), 15, 3));
                                    resultvalidation.Add(item.TotalFullTimeEmployeeInBVIString.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}b."), nameof(item.TotalFullTimeEmployeeInBVI)));
                                    resultvalidation.Add(item.TotalFullTimeEmployeeInBVIString.IsDecimal(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"9{header}.{num}b."), nameof(item.TotalFullTimeEmployeeInBVI), 15, 3));

                                    if (resultvalidation.Where(x => x.FieldName == nameof(item.TotalNoFullTimeEmployee) && x.FieldName == nameof(item.TotalFullTimeEmployeeInBVI)).ToList().Count == 0)
                                    {
                                        item.TotalNoFullTimeEmployee = item.TotalNoFullTimeEmployeeString.ToDecimal();
                                        item.TotalFullTimeEmployeeInBVI = item.TotalFullTimeEmployeeInBVIString.ToDecimal();
                                        resultvalidation.Add(item.TotalNoFullTimeEmployee.ValueIsGreater(item.TotalFullTimeEmployeeInBVI, string.Format(ValidationMessages.VALUE_EXCEED_MESSAGE, "Employee", $"9{header}.{num}b."), nameof(item.TotalFullTimeEmployeeInBVI)));
                                    }

                                    //BVI Declaration Changes 2.0
                                    if (input.economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                    {
                                        resultvalidation.Add(item.TotalNoCorporateLegalEmployeeString.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}."), nameof(item.TotalNoCorporateLegalEmployee)));
                                        resultvalidation.Add(item.TotalNoCorporateLegalEmployeeString.IsDecimal(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"9{header}.{num}."), nameof(item.TotalNoCorporateLegalEmployee), 15, 3));
                                        if (resultvalidation.Where(x => x.FieldName == nameof(item.TotalNoCorporateLegalEmployee)).ToList().Count == 0)
                                        {
                                            item.TotalNoCorporateLegalEmployee = item.TotalNoCorporateLegalEmployeeString.ToDecimal();
                                            resultvalidation.Add(item.TotalNoCorporateLegalEmployee.ValueIsGreater(item.TotalNoFullTimeEmployee, string.Format(ValidationMessages.VALUE_EXCEED_MESSAGE, "Employee", $"9{header}.{num}a."), nameof(item.TotalNoCorporateLegalEmployee)));
                                        }

                                        if (item.TotalFullTimeEmployeeInBVI.HasValue && item.TotalFullTimeEmployeeInBVI.Value > 0)
                                        {
                                            if (item.EmployeeQualificationDetails == null || (item.EmployeeQualificationDetails?.Where(x => !x.IsDeleted).Count() == 0))
                                            {
                                                resultvalidation.Add(new ValidationResultDto { FieldName = "EmployeeDetail", ValidationString = $"The user should add employee detail For 9{header}.{num}c." });
                                            }

                                            resultvalidation.AddRange(ValidationHelper.ValidateEmployeeUploadDetail(item.EmployeeQualificationDetails, $" For 9{header}.{num}c."));
                                        }
                                    }
                                    else
                                    {
                                        //Old Declaration
                                        if ((resultvalidation.Any(x => !string.IsNullOrEmpty(x.FieldName) && x.FieldName.Contains(nameof(item.TotalFullTimeEmployeeInBVI)))) && (item.TotalFullTimeEmployeeInBVIString.ToDecimal() > 0))
                                        {
                                            resultvalidation.AddRange(ValidationHelper.ValidateEmployeeUploadDetail(item.EmployeeQualificationDetails, $" For 9{header}.{num}c."));
                                        }
                                    }
                                }
                            }

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 5, input.economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                //Premises Table
                                num++;
                                if (item.ReleventActivityValue != RelevantActivity.HoldBA || input.economicSubstanceDeclarationDto.FiscalEndDate.IsLessThan(EssConstants.DeclarationChanges_2_0_Date) || (item.DoesEntityManageEquity.HasValue && item.DoesEntityManageEquity.Value))
                                {
                                    resultvalidation.AddRange(ValidationHelper.ValidatePermisesUploadDetail(item.PremisesAddress, string.Format(ValidationMessages.DETAIL_REQUIRED_MESSAGE, $"9{header}.{num}a.")));
                                }
                            }

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 6, input.economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                num++;

                                if (!item.DoesLegalEntityConductCIGA.HasValue || !item.DoesLegalEntityConductCIGA.Value)
                                {
                                    //high risk
                                    resultvalidation.Add(item.IsLegalEntityHighRiskString.IsBoolean(String.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, $"9{header}.{num}a."), nameof(item.IsLegalEntityHighRisk)));
                                    resultvalidation.Add(item.IsLegalEntityHighRisk.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.IsLegalEntityHighRisk)));

                                    resultvalidation.Add(item.DoesEntityProvideEvidenceString.IsBoolean(String.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, $"9{header}.{num}b."), nameof(item.DoesEntityProvideEvidence)));
                                    resultvalidation.Add(item.DoesEntityProvideEvidence.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}b."), nameof(item.DoesEntityProvideEvidence), new List<PairMemberValue>() { new PairMemberValue { Member = item.IsLegalEntityHighRisk } }));

                                    if (input.economicSubstanceDeclarationDto.FiscalEndDate.IsLessThan(EssConstants.DeclarationChanges_2_0_Date))
                                    {
                                        resultvalidation.Add(item.TotalGrossAnnualIncomeString.IsDecimal(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"9{header}.{num}d."), "TotalGrossAnnualIncomeE", 15, 2));
                                    }
                                    resultvalidation.Add(item.GrossIncomeRoyalitiesString.IsDecimal(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"9{header}.{num}e."), "GrossIncomeRoyalitiesE", 15, 2));
                                    resultvalidation.Add(item.GrossIncomeGainsString.IsDecimal(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"9{header}.{num}f."), "GrossIncomeGainsE", 15, 2));

                                    resultvalidation.Add(item.HighRiskDocuments.AnyUploadedDocument(String.Format(ValidationMessages.UPLOAD_ATTACHMENT_MESSAGE, $"9{header}.{num}c."), "UploadDocumentHighRisk",
                                        new List<PairMemberValue>() { new PairMemberValue { Member = item.IsLegalEntityHighRisk },
                                                                  new PairMemberValue { Member = item.DoesEntityProvideEvidence }}));

                                    //high risk add warning message
                                    if (item.IsLegalEntityHighRisk.HasValue && item.IsLegalEntityHighRisk.Value)
                                    {
                                        if (input.economicSubstanceDeclarationDto.FiscalEndDate.IsLessThan(EssConstants.DeclarationChanges_2_0_Date))
                                        {
                                            var validationIPTotalGrossAnnualIncomeWarning = item.TotalGrossAnnualIncomeString.IsNullOrEmpty(string.Format(ValidationMessages.ISRECOMEND_MESSAGE, $"9{header}.{num}d."), nameof(item.TotalGrossAnnualIncome));
                                            validationIPTotalGrossAnnualIncomeWarning.ValidationType = Bdo.Ess.Common.EconomicSubstance.ValidationType.Warning;
                                            resultvalidation.Add(validationIPTotalGrossAnnualIncomeWarning);
                                        }

                                        List<ValidationResultDto> validationWarrning = new List<ValidationResultDto>()
                                            {
                                                item.GrossIncomeRoyalitiesString.IsNullOrEmpty(string.Format(ValidationMessages.ISRECOMEND_MESSAGE, $"9{header}.{num}e."), nameof(item.GrossIncomeRoyalities)),
                                                item.GrossIncomeGainsString.IsNullOrEmpty(string.Format(ValidationMessages.ISRECOMEND_MESSAGE, $"9{header}.{num}f."), nameof(item.GrossIncomeGains))
                                            };

                                        validationWarrning.ToList().ForEach(n => n.ValidationType = ValidationType.Warning);
                                        resultvalidation.AddRange(validationWarrning);
                                    }
                                }

                                //BVI Declaration Changes 2.0
                                if ((item.IsLegalEntityHighRisk.HasValue && item.IsLegalEntityHighRisk.Value) && input.economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                {
                                    resultvalidation.Add(item.GrossIncomeOthersString.IsDecimal(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"9{header}.{num}g."), "GrossIncomeOthersE", 15, 2));
                                    var validationGrossIncomeOthersWarning = item.GrossIncomeOthersString.IsNullOrEmpty(string.Format(ValidationMessages.ISRECOMEND_MESSAGE, $"9{header}.{num}g."), nameof(item.GrossIncomeOthers));
                                    validationGrossIncomeOthersWarning.ValidationType = Bdo.Ess.Common.EconomicSubstance.ValidationType.Warning;
                                    resultvalidation.Add(validationGrossIncomeOthersWarning);
                                    resultvalidation.Add(item.TangibleAsset.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}h."), nameof(item.TangibleAsset)));
                                    resultvalidation.Add(item.TangibleAsset.IsMaxLength(string.Format(ValidationMessages.VALID_LENGTH_MESSAGE, 255, $"9{header}.{num}h."), nameof(item.TangibleAsset), 255));
                                    resultvalidation.Add(item.TangibleAssetIncome.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}i."), nameof(item.TangibleAssetIncome)));
                                    resultvalidation.Add(item.TangibleAssetEmployeeResponsibility.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}j."), nameof(item.TangibleAssetEmployeeResponsibility)));
                                    resultvalidation.Add(item.HistoryofStrategicDecisionsInBVI.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}k."), nameof(item.HistoryofStrategicDecisionsInBVI)));
                                    resultvalidation.Add(item.HistoryofTradingActivityIncome.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}l."), nameof(item.HistoryofTradingActivityIncome)));
                                }
                            }

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 7, input.economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                // other CIGA
                                num++;
                                if (!item.IsLegalEntityHighRisk.HasValue || !item.IsLegalEntityHighRisk.Value)
                                {
                                    resultvalidation.Add(item.DoesLegalEntityConductCIGAString.IsBoolean(String.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, $"9{header}.{num}a."), nameof(item.DoesLegalEntityConductCIGA)));
                                    resultvalidation.Add(item.DoesLegalEntityConductCIGA.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.DoesLegalEntityConductCIGA)));

                                    resultvalidation.Add(item.DoesEntityProvideEvidenceroRString.IsBoolean(String.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, $"9{header}.{num}b."), nameof(item.DoesEntityProvideEvidenceroRebut)));
                                    resultvalidation.Add(item.DoesEntityProvideEvidenceroRebut.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}b."), nameof(item.DoesEntityProvideEvidenceroRebut),
                                        new List<PairMemberValue>() { new PairMemberValue { Member = item.DoesLegalEntityConductCIGA } }));
                                    resultvalidation.Add(item.OtherCIGADocuments.AnyUploadedDocument(String.Format(ValidationMessages.UPLOAD_ATTACHMENT_MESSAGE, $"9{header}.{num}c."), "UploadOtherCIGA",
                                        new List<PairMemberValue>() { new PairMemberValue { Member = item.DoesLegalEntityConductCIGA },
                                                                  new PairMemberValue { Member = item.DoesEntityProvideEvidenceroRebut }}));
                                }

                                //BVI Declaration Changes 2.0
                                if ((item.DoesLegalEntityConductCIGA.HasValue && item.DoesLegalEntityConductCIGA.Value) && input.economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                {
                                    resultvalidation.Add(item.RelevantIPAsset.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}d."), nameof(item.RelevantIPAsset)));
                                    resultvalidation.Add(item.RelevantIPAsset.IsMaxLength(string.Format(ValidationMessages.VALID_LENGTH_MESSAGE, 255, $"9{header}.{num}d."), nameof(item.RelevantIPAsset), 255));
                                    resultvalidation.Add(item.IPAssetsInBVI.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}e."), nameof(item.IPAssetsInBVI)));
                                    resultvalidation.Add(item.IPAssetsEmployeeResponsibility.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}f."), nameof(item.IPAssetsEmployeeResponsibility)));
                                    resultvalidation.Add(item.ConcreteEvidenceDecisionInBVI.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}g."), nameof(item.ConcreteEvidenceDecisionInBVI)));
                                }
                            }

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 8, input.economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                // Income generation activities
                                // exception for Intellectulaul
                                num++;

                                if ((item.ReleventActivityValue == RelevantActivity.IntelBA &&
                                   item.IsLegalEntityHighRisk.HasValue && !item.IsLegalEntityHighRisk.Value
                                   && item.DoesLegalEntityConductCIGA.HasValue && !item.DoesLegalEntityConductCIGA.Value) ||

                                       item.ReleventActivityValue != RelevantActivity.IntelBA)
                                {
                                    resultvalidation.Add(item.ConductedCIGAActivity?.IsOtherSelected($"Other Option is required.", nameof(item.CIGAOtherDetail), item.CIGAOtherDetail));
                                    if (item.ConductedCIGAActivity?.Where(x => !x.IsDeleted && x.CIGAActivity.ActivityDetails.Contains("Other")).Count() > 0 && !string.IsNullOrEmpty(item.CIGAOtherDetail))
                                    {
                                        resultvalidation.Add(item.CIGAOtherDetail.IsMaxLength(string.Format(ValidationMessages.VALID_LENGTH_MESSAGE, 100, $"9{header}.{num}a."), nameof(item.CIGAOtherDetail), 100));
                                    }

                                    //BVI Declaration Changes 2.0
                                    if (input.economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                    {
                                        resultvalidation.Add(item.ConductedCIGAActivity.ValidateConductedCIGAActivity($"The user should add CIGA activity.", nameof(item.ConductedCIGAActivity)));
                                    }
                                }
                            }

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 9, input.economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                // Outsourcing
                                // exception for Intellectulaul
                                num++;

                                if ((item.ReleventActivityValue == RelevantActivity.IntelBA && (item.DoesLegalEntityConductCIGA.HasValue && item.DoesLegalEntityConductCIGA.Value)) ||

                                                 item.ReleventActivityValue != RelevantActivity.IntelBA)
                                {

                                    resultvalidation.Add(item.HasAnyIncomeBeenOutsourcedString.IsBoolean(String.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, $"9{header}.{num}a."),
                                     nameof(item.HasAnyIncomeBeenOutsourced)));

                                    resultvalidation.Add(item.HasAnyIncomeBeenOutsourced.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}a."),
                                     nameof(item.HasAnyIncomeBeenOutsourced)));
                                    resultvalidation.AddRange(ValidationHelper.ValidateOutsourceUploadDetail(item.ServiceProviders, string.Format(ValidationMessages.DETAIL_REQUIRED_MESSAGE, $"9{header}.{num}b."), new List<PairMemberValue>() { new PairMemberValue { Member = item.HasAnyIncomeBeenOutsourced } }));

                                    //BVI Declaration Changes 2.0
                                    if (input.economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                    {
                                        resultvalidation.Add(item.TotalExpenditureIncurredInBVIString.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}c."), nameof(item.TotalExpenditureIncurredInBVI)));
                                    }
                                    resultvalidation.Add(item.TotalExpenditureIncurredInBVIString.IsDecimal(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"9{header}.{num}c."), nameof(item.TotalExpenditureIncurredInBVI), 15, 2));
                                }

                            }
                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 10, input.economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                // equipment
                                num++;
                                resultvalidation.Add(item.EquipmentDescription.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.EquipmentDescription)));
                                resultvalidation.Add(item.EquipmentDescription.IsMaxLength(String.Format(ValidationMessages.VALID_LENGTH_MESSAGE, 255, $"9{header}.{num}a."), nameof(item.EquipmentDescription), 255));
                            }
                            //// end
                            validationResultMainDto1.ValidationResultDto = resultvalidation.Where(x => x != null && !string.IsNullOrEmpty(x.FieldName)).ToList();
                            validationResult.Add(validationResultMainDto1);
                            resultvalidation.Clear();
                            header++;
                        }
                    }

                    resultvalidation.Add(input.economicSubstanceDeclarationDto.SupportingComments.IsMaxLength(String.Format(ValidationMessages.VALID_LENGTH_MESSAGE, 510, "10a."), nameof(input.economicSubstanceDeclarationDto.SupportingComments), 510));
                    resultvalidation = resultvalidation.Where(x => !string.IsNullOrEmpty(x.FieldName)).Distinct().ToList();
                    validationResult.Add(new ValidationResultMainDto { ActivityName = "Step5", ValidationResultDto = new List<ValidationResultDto>(resultvalidation) });
                    resultvalidation.Clear();
                    return validationResult;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "ValidationImportRules encountered an exception.");
                    throw;
                }
            }
        }

        public async Task<List<ValidationResultMainDto>> ValidationRules(EconomicSubstanceStepDto input)
        {

            EconomicSubstanceDeclarationDto economicSubstanceDeclarationDto = input.EconomicSubstanceDeclarationDto.FixDateTime();

            // Need the Corporate Entity to validate that the Fiscal Period Start does not begin before the Incorporation Date
            economicSubstanceDeclarationDto.CorporateEntity =
                await _corporateEntityMicroservice.Call(x => x.GetEntity(new GetEntityInput { Id = economicSubstanceDeclarationDto.CorporateEntityId }));

            List<ValidationResultDto> resultvalidation = new List<ValidationResultDto>();

            var validationResult = new List<ValidationResultMainDto>();

            switch (input.StepLevel)
            {
                case 0:
                    {
                        ValidationResultMainDto validationResultMainDto = new ValidationResultMainDto();
                        resultvalidation.Add(economicSubstanceDeclarationDto.IsFinaincialCanChange.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "6a."), nameof(economicSubstanceDeclarationDto.IsFinaincialCanChange)));
                        resultvalidation.Add(economicSubstanceDeclarationDto.FiscalStartDate.IsNullOrEmpty("Financial Period start date cannot not be empty.", nameof(economicSubstanceDeclarationDto.FiscalStartDate), economicSubstanceDeclarationDto.IsFinaincialCanChange));
                        resultvalidation.Add(economicSubstanceDeclarationDto.FiscalEndDate.IsNullOrEmpty("Financial Period end date cannot not be empty.", nameof(economicSubstanceDeclarationDto.FiscalEndDate), economicSubstanceDeclarationDto.IsFinaincialCanChange));

                        // need to validate the rest if there is a value for the start and end  date
                        if (!economicSubstanceDeclarationDto.FiscalStartDate.IsDateNull() && !economicSubstanceDeclarationDto.FiscalEndDate.IsDateNull() && economicSubstanceDeclarationDto.IsFinaincialCanChange.HasValue)
                        {
                            var repo = GetRepository<EconomicSubstanceDeclaration>();
                            var result = repo.Query(x => x.CorporateEntityId == economicSubstanceDeclarationDto.CorporateEntityId).ToList();

                            var isAnyDraftDeclaration = result.Any(x => x.Id != economicSubstanceDeclarationDto.Id && !x.IsDeleted && x.Status == EconomicSubstanceStatus.Draft);

                            if (isAnyDraftDeclaration)
                            {
                                resultvalidation.Add(new ValidationResultDto
                                {
                                    FieldName = nameof(economicSubstanceDeclarationDto.FiscalEndDate),
                                    ValidationString = "A draft declaration exist, please review before creating a new declaration",
                                    ValidationType = ValidationType.Warning
                                });
                            }

                            resultvalidation.Add(economicSubstanceDeclarationDto.IsPeriodStartsBeforeIncorporationDate("The financial period starts before the Date of Incorporation"));


                            var overlapMessage = "Invalid Financial period start date. You cannot create declarations with overlapping Financial periods.";
                            resultvalidation.Add(ValidationHelper.IsVaildFiscalYear(economicSubstanceDeclarationDto, result, overlapMessage));

                            if (!resultvalidation.Any(v => v.ValidationString == overlapMessage))
                                resultvalidation.Add(economicSubstanceDeclarationDto.IsValidAfterPreviousDeclaration(result, "There is a previous declaration with an end date of {0}, therefore, the start date should be {1}."));

                            resultvalidation.Add(economicSubstanceDeclarationDto.IsExpectedStartDate(result, "The selected start date was not expected. The expected date was {0}."));
                            resultvalidation.Add(economicSubstanceDeclarationDto.IsExpectedFinancialEndDate("The selected Financial Period End Date was not expected. The Financial Period End Date cannot be prior to {0}."));
                            resultvalidation.Add(economicSubstanceDeclarationDto.IsPeriodMoreThanAYear("Financial period cannot be longer than 12 months"));


                            resultvalidation.Add(economicSubstanceDeclarationDto.IsStartDateGreaterThanEndDate("Financial end date must be greater than Financial start date"));

                            resultvalidation.Add(economicSubstanceDeclarationDto.FiscalEndDate.IsEndDateGraterThanCurrentDate("Financial end date must be less than the current date", nameof(economicSubstanceDeclarationDto.FiscalEndDate)));

                            // End dates need to be managed together, and is dependent on the entity's status
                            switch (economicSubstanceDeclarationDto.CorporateEntity.StatusText.ToLower())
                            {
                                default:
                                    resultvalidation.Add(economicSubstanceDeclarationDto.IsPeriodLessThanAYear("The financial period is less than 12 months"));
                                    break;
                            }

                        }
                        validationResultMainDto.ActivityName = "Step1";
                        validationResultMainDto.ValidationResultDto = resultvalidation.Where(x => !string.IsNullOrEmpty(x.FieldName)).ToList();
                        validationResult.Add(validationResultMainDto);
                        break;
                    }
                case 1:
                    {
                        //BVI Declaration Changes 2.0
                        ValidationResultMainDto validationResultMainDto = new ValidationResultMainDto();
                        if (!input.EconomicSubstanceDeclarationDto.EsEntityDetails.Any(x => !x.IsDeleted))
                        {
                            resultvalidation.Add(new ValidationResultDto { FieldName = nameof(input.EconomicSubstanceDeclarationDto.EsEntityDetails), ValidationString = "The user should add entity detail" });
                        }
                        else
                        {
                            foreach (var item in input.EconomicSubstanceDeclarationDto.EsEntityDetails.Where(x => !x.IsDeleted).ToList())
                            {
                                var validationTINWarning = item.IdentificationNumber.IsNullOrEmpty(string.Format("You must enter the TIN# if it exists {0}", $"6f."), nameof(item.IdentificationNumber));
                                validationTINWarning.ValidationType = Bdo.Ess.Common.EconomicSubstance.ValidationType.Warning;
                                resultvalidation.Add(validationTINWarning);

                                var validationTotalGrossAnnualIncomeWarning = item.TotalGrossAnnualIncome.IsNullOrEmpty(string.Format(ValidationMessages.ISRECOMEND_MESSAGE, $"6g."), nameof(item.TotalGrossAnnualIncome));
                                validationTotalGrossAnnualIncomeWarning.ValidationType = Bdo.Ess.Common.EconomicSubstance.ValidationType.Warning;
                                resultvalidation.Add(validationTotalGrossAnnualIncomeWarning);

                                resultvalidation.Add(item.IsSameAsRegisteredAddress.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"6h."), nameof(item.IsSameAsRegisteredAddress)));

                                if (item.IsSameAsRegisteredAddress.HasValue)
                                {
                                    resultvalidation.Add(item.AddressLine1.IsNullOrEmpty(string.Format("Response for Address Line 1 is required {0}", $"6h.1"), nameof(item.AddressLine1)));
                                    resultvalidation.Add(item.Country.IsCountryNullOrNA(string.Format("Response for Country is required {0}", $"6h.1."), nameof(item.Country)));
                                }

                                var validationMNEWarning = item.MNEGroupName.IsNullOrEmpty(string.Format(ValidationMessages.ISRECOMEND_MESSAGE, "6i."), nameof(item.MNEGroupName));
                                validationMNEWarning.ValidationType = Bdo.Ess.Common.EconomicSubstance.ValidationType.Warning;
                                resultvalidation.Add(validationMNEWarning);


                                resultvalidation.Add(item.DoesEntityHaveUltimateParent.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, "6j."), nameof(item.DoesEntityHaveUltimateParent)));

                                if (item.DoesEntityHaveUltimateParent.HasValue && item.DoesEntityHaveUltimateParent.Value)
                                {
                                    resultvalidation.Add(ValidationHelper.EntityAdditionalDetail(item.EsEntityAdditionalDetails.Where(i => i.EntityType == ParentEntityType.Ultimate).ToList(), string.Format(ValidationMessages.DETAIL_REQUIRED_MESSAGE, "6j.1"), "UltimateParent"));
                                }

                                resultvalidation.Add(item.DoesEntityHaveImmediateParent.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, "6k."), nameof(item.DoesEntityHaveImmediateParent)));

                                if (item.DoesEntityHaveImmediateParent.HasValue && item.DoesEntityHaveImmediateParent.Value)
                                {
                                    resultvalidation.Add(ValidationHelper.EntityAdditionalDetail(item.EsEntityAdditionalDetails.Where(i => i.EntityType == ParentEntityType.Immediate).ToList(), string.Format(ValidationMessages.DETAIL_REQUIRED_MESSAGE, "6k.1"), "ImmediateParent"));
                                }

                            }
                        }
                        validationResultMainDto.ActivityName = "EntityDetails";
                        validationResultMainDto.ValidationResultDto = resultvalidation.Where(x => !string.IsNullOrEmpty(x.FieldName)).ToList();
                        validationResult.Add(validationResultMainDto);
                        break;
                    }
                case 2:
                    {
                        ValidationResultMainDto validationResultMainDto = new ValidationResultMainDto();
                        if (input.EconomicSubstanceDeclarationDto.RelevantActivities.Where(x => x.IsChecked && !x.IsDeleted).ToList().Count == 0)
                        {
                            resultvalidation.Add(new ValidationResultDto { FieldName = nameof(input.EconomicSubstanceDeclarationDto.RelevantActivities), ValidationString = "The user should select at least one relevant activity" });
                        }
                        else
                        {
                            foreach (var item in input.EconomicSubstanceDeclarationDto.RelevantActivities.Where(x => x.IsChecked && !x.IsDeleted && x.RelevantActivityName != "None").ToList())
                            {

                                if (item.IsCarriedForPartFinancialPeriod.HasValue && item.IsCarriedForPartFinancialPeriod.Value)
                                {
                                    resultvalidation.Add(item.StartDate.IsNullOrEmpty(item.RelevantActivityName + "-Start Date is Empty", nameof(input.EconomicSubstanceDeclarationDto.RelevantActivities)));
                                    resultvalidation.Add(item.EndDate.IsNullOrEmpty(item.RelevantActivityName + "-End Date is Empty", nameof(input.EconomicSubstanceDeclarationDto.RelevantActivities)));
                                    resultvalidation.Add(item.IsStartDateGreaterThanEndDate(item.RelevantActivityName + "- End date must be greater than start date", nameof(input.EconomicSubstanceDeclarationDto.RelevantActivities)));
                                    resultvalidation.Add(item.IsDateWithinFiscalYear(economicSubstanceDeclarationDto.FiscalStartDate, economicSubstanceDeclarationDto.FiscalEndDate, item.RelevantActivityName + "- -start date and end date must be within the Financial date range", nameof(input.EconomicSubstanceDeclarationDto.RelevantActivities)));
                                }
                            }
                        }
                        validationResultMainDto.ActivityName = "Step2";
                        validationResultMainDto.ValidationResultDto = resultvalidation.Where(x => !string.IsNullOrEmpty(x.FieldName)).ToList();
                        validationResult.Add(validationResultMainDto);
                        break;
                    }

                case 3:
                    {
                        ValidationResultMainDto validationResultMainDto = new ValidationResultMainDto();
                        validationResultMainDto.ActivityName = "Step3";
                        resultvalidation.Add(economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8a."),
                                    nameof(economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI)));

                        resultvalidation.Add(economicSubstanceDeclarationDto.JurisdictionTaxResident.IsCountryNullOrNA(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8b."),
                            nameof(economicSubstanceDeclarationDto.JurisdictionTaxResident),
                             new List<PairMemberValue>() { new PairMemberValue { Member = economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));

                        //BVI Declaration Changes 2.0
                        if (economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                        {
                            resultvalidation.Add(economicSubstanceDeclarationDto.TaxPayerIdentificationNumber.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8b.1"), nameof(economicSubstanceDeclarationDto.TaxPayerIdentificationNumber),
                                 new List<PairMemberValue>() { new PairMemberValue { Member = economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));
                        }
                        else
                        {
                            resultvalidation.Add(economicSubstanceDeclarationDto.DoesEntityHaveParentEntity.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8c."),
                                            nameof(economicSubstanceDeclarationDto.DoesEntityHaveParentEntity),
                            new List<PairMemberValue>() { new PairMemberValue { Member = economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));

                            resultvalidation.Add(economicSubstanceDeclarationDto.ParentEntityName.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8c.1"), nameof(economicSubstanceDeclarationDto.ParentEntityName),
                                new List<PairMemberValue>() { new PairMemberValue { Member = economicSubstanceDeclarationDto.DoesEntityHaveParentEntity },
                                                          new PairMemberValue { Member = economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));

                            resultvalidation.Add(economicSubstanceDeclarationDto.ParentJurisdiction.IsCountryNull(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8c.3"),
                                nameof(economicSubstanceDeclarationDto.ParentJurisdiction), new List<PairMemberValue>()
                                { new PairMemberValue { Member = economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI },
                               new PairMemberValue { Member = economicSubstanceDeclarationDto.DoesEntityHaveParentEntity } }));


                            resultvalidation.Add(economicSubstanceDeclarationDto.EntityIncorporationNumber.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8c.4"),
                                nameof(economicSubstanceDeclarationDto.EntityIncorporationNumber),
                                new List<PairMemberValue>() { new PairMemberValue { Member = economicSubstanceDeclarationDto.DoesEntityHaveParentEntity },
                                                          new PairMemberValue { Member = economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI }}));
                        }

                        resultvalidation.Add(economicSubstanceDeclarationDto.IsEvidenceNonResidenceOrTreatment.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, "8d."),
                            nameof(economicSubstanceDeclarationDto.IsEvidenceNonResidenceOrTreatment),
                            new List<PairMemberValue>() { new PairMemberValue { Member = economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI } }));

                        resultvalidation.Add(economicSubstanceDeclarationDto.EvidenceOfNonResidanceDocument.AnyUploadedDocument(String.Format(ValidationMessages.UPLOAD_ATTACHMENT_MESSAGE, "8d.1."), "ResidanceDocument",
                            new List<PairMemberValue>()
                            { new PairMemberValue { Member = economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI },
                              new PairMemberValue { Member = economicSubstanceDeclarationDto.IsEvidenceNonResidenceOrTreatment }
                            }));

                        resultvalidation.Add(economicSubstanceDeclarationDto.EvidenceProvisionalTreatmentDocuments.AnyUploadedDocument(String.Format(ValidationMessages.UPLOAD_ATTACHMENT_MESSAGE, "8d.2."), "ResidanceDocument",
                            new List<PairMemberValue>()
                            { new PairMemberValue { Member = economicSubstanceDeclarationDto.DoesEntityMakeClaimOutisedBVI  },
                             new PairMemberValue { Member = economicSubstanceDeclarationDto.IsEvidenceNonResidenceOrTreatment, Value=false }
                            }));


                        validationResultMainDto.ValidationResultDto = resultvalidation.Where(x => !string.IsNullOrEmpty(x.FieldName)).ToList();
                        validationResult.Add(validationResultMainDto);
                        break;
                    }

                case 4:
                    {
                        char header = 'a';
                        foreach (var item in economicSubstanceDeclarationDto.RelevantActivities.Where(x => x.IsChecked && !x.IsDeleted && x.RelevantActivityName != "None"))
                        {
                            ValidationResultMainDto validationResultMainDto = new ValidationResultMainDto();

                            validationResultMainDto.ActivityName = item.RelevantActivityName;
                            validationResultMainDto.HeaderSeq = header;
                            int num = 0;
                            resultvalidation.Clear();

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 2, economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                //Turnover
                                //GrossIncome
                                num++;
                                resultvalidation.Add(item.TotalTurnover.IsNullorNoValue(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.TotalTurnover)));

                                //BVI Declaration Changes 2.0
                                if (economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                {
                                    if (!item.IsLegalEntityHighRisk.HasValue || !item.IsLegalEntityHighRisk.Value)
                                    {
                                        resultvalidation.Add(item.GrossIncomeType.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}b."), nameof(item.GrossIncomeType)));
                                        resultvalidation.Add(item.GrossIncomeType.IsMaxLength(string.Format(ValidationMessages.VALID_LENGTH_MESSAGE, 255, $"9{header}.{num}b."), nameof(item.GrossIncomeType), 255));
                                    }

                                    if (item.ReleventActivityValue != RelevantActivity.HoldBA)
                                    {
                                        resultvalidation.Add(item.TotalAssetsValue.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}c."), nameof(item.TotalAssetsValue)));
                                        resultvalidation.Add(item.NetAssetsValue.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}d."), nameof(item.NetAssetsValue)));
                                    }
                                }
                            }

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 1, economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                num++;
                                // Direction and Management validation
                                resultvalidation.Add(ValidationHelper.ValidateManagementDetail(item.ManagementDetails,
                                     string.Format(ValidationMessages.DETAIL_REQUIRED_MESSAGE, $"9{header}.{num}b.")));
                                resultvalidation.Add(item.NoofConductedMeeting.ValueIsGreater(item.NoofMeetingHeldInBVI, string.Format(ValidationMessages.VALUE_EXCEED_MESSAGE, "# of Meetings", $"9{header}.{num}c."), nameof(item.NoofMeetingHeldInBVI)));

                                //BVI Declaration Changes 2.0
                                if (economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                {
                                    resultvalidation.Add(item.IsActivityDirectedInBVI.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.IsActivityDirectedInBVI)));
                                    resultvalidation.Add(item.NoofConductedMeeting.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}c."), nameof(item.NoofConductedMeeting)));
                                    resultvalidation.Add(item.NoofMeetingHeldInBVI.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}d."), nameof(item.NoofMeetingHeldInBVI)));

                                    if (!string.IsNullOrEmpty(item.NoofMeetingHeldInBVIString))
                                        resultvalidation.Add(item.NoofMeetingHeldInBVIString.IsInteger(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}d."), nameof(item.NoofMeetingHeldInBVI), 0, int.MaxValue));


                                    resultvalidation.Add(item.NoofMeetingHeldInBVI.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}d."), nameof(item.NoofMeetingHeldInBVI)));
                                    if (item.NoofMeetingHeldInBVI.HasValue && item.NoofMeetingHeldInBVI.Value > 0)
                                    {
                                        resultvalidation.Add(item.IsMeetingMinutesInBVI.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}f."), nameof(item.IsMeetingMinutesInBVI)));
                                        resultvalidation.AddRange(ValidationHelper.AttendMeetingDetails(item.AttendMeetingDetails, $" For 9{header}.{num}e.", "MeetingTable"));
                                    }
                                    resultvalidation.Add(item.NoofQuorumBoardMeeting.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}g."), nameof(item.NoofQuorumBoardMeeting)));
                                    resultvalidation.Add(item.IsQuorumBoardMeetingInBVI.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}h."), nameof(item.IsQuorumBoardMeetingInBVI)));
                                }
                            }


                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 3, economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                //Expenditure 
                                num++;
                                resultvalidation.Add(item.TotalExpediture.IsNullorNoValue(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.TotalExpediture)));
                                resultvalidation.Add(item.TotalExpeditureInBVI.IsNullorNoValue(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}b."), nameof(item.TotalExpeditureInBVI)));
                                resultvalidation.Add(item.TotalExpediture.ValueIsGreater(item.TotalExpeditureInBVI, string.Format(ValidationMessages.VALUE_EXCEED_MESSAGE, "Expediture", $"9{header}.{num}b."), nameof(item.TotalExpeditureInBVI)));
                            }

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 0, economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                num++;
                                //pure equity holding business
                                //BVI Declaration Changes 2.0
                                if (economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                {
                                    resultvalidation.Add(item.DoesEntityManageEquity.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.DoesEntityManageEquity)));
                                    if (item.DoesEntityManageEquity.HasValue && !item.DoesEntityManageEquity.Value)
                                        resultvalidation.Add(item.DoesEntityComplyItsStatutoryObligations.IsNullOrEmpty(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}b."), nameof(item.DoesEntityComplyItsStatutoryObligations)));
                                }
                                else
                                {
                                    //Old Declaration
                                    var validationWarrning = item.DoesEntityManageEquity.IsNullOrEmpty($"Response is recommended for Question 9{header}.{num}a.", nameof(item.DoesEntityManageEquity));
                                    validationWarrning.ValidationType = ValidationType.Warning;
                                    resultvalidation.Add(validationWarrning);
                                }
                            }


                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 4, economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                // employee
                                num++;

                                if (item.ReleventActivityValue != RelevantActivity.HoldBA || economicSubstanceDeclarationDto.FiscalEndDate.IsLessThan(EssConstants.DeclarationChanges_2_0_Date) || (item.DoesEntityManageEquity.HasValue && item.DoesEntityManageEquity.Value))
                                {
                                    resultvalidation.Add(item.TotalNoFullTimeEmployee.IsNullorNoValue(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.TotalNoFullTimeEmployee)));
                                    resultvalidation.Add(item.TotalFullTimeEmployeeInBVI.IsNullorNoValue(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}b."), nameof(item.TotalFullTimeEmployeeInBVI)));
                                    resultvalidation.Add(item.TotalNoFullTimeEmployee.ValueIsGreater(item.TotalFullTimeEmployeeInBVI, string.Format(ValidationMessages.VALUE_EXCEED_MESSAGE, "Employee", $"9{header}.{num}b."), nameof(item.TotalFullTimeEmployeeInBVI)));


                                    //BVI Declaration Changes 2.0
                                    if (economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                    {
                                        resultvalidation.Add(item.TotalNoCorporateLegalEmployee.IsNullorNoValue(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}."), nameof(item.TotalNoCorporateLegalEmployee)));
                                        resultvalidation.Add(item.TotalNoCorporateLegalEmployee.ValueIsGreater(item.TotalNoFullTimeEmployee, string.Format(ValidationMessages.VALUE_EXCEED_MESSAGE, "Employee", $"9{header}.{num}a."), nameof(item.TotalNoCorporateLegalEmployee)));

                                        if (item.TotalFullTimeEmployeeInBVI.HasValue && item.TotalFullTimeEmployeeInBVI.Value > 0)
                                        {
                                            if (item.EmployeeQualificationDetails == null || (item.EmployeeQualificationDetails?.Where(x => !x.IsDeleted).Count() == 0))
                                            {
                                                resultvalidation.Add(new ValidationResultDto { FieldName = "EmployeeDetail", ValidationString = $"The user should add employee detail For 9{header}.{num}c." });
                                            }

                                            resultvalidation.AddRange(ValidationHelper.ValidateEmployeeDetail(item.EmployeeQualificationDetails, $" For 9{header}.{num}c."));
                                        }
                                    }
                                }
                            }


                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 5, economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                //Premises Table
                                num++;
                                if (item.ReleventActivityValue != RelevantActivity.HoldBA || economicSubstanceDeclarationDto.FiscalEndDate.IsLessThan(EssConstants.DeclarationChanges_2_0_Date) || (item.DoesEntityManageEquity.HasValue && item.DoesEntityManageEquity.Value))
                                {
                                    resultvalidation.Add(ValidationHelper.ValidatePermisesDetail(item.PremisesAddress, string.Format(ValidationMessages.DETAIL_REQUIRED_MESSAGE, $"9{header}.{num}a."), null));
                                }
                            }

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 6, economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                num++;
                                if (!item.DoesLegalEntityConductCIGA.HasValue || !item.DoesLegalEntityConductCIGA.Value)
                                {
                                    //high risk
                                    resultvalidation.Add(item.IsLegalEntityHighRisk.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.IsLegalEntityHighRisk)));
                                    resultvalidation.Add(item.DoesEntityProvideEvidence.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}b."), nameof(item.DoesEntityProvideEvidence), new List<PairMemberValue>() { new PairMemberValue { Member = item.IsLegalEntityHighRisk } }));
                                    resultvalidation.Add(item.HighRiskDocuments.AnyUploadedDocument(String.Format(ValidationMessages.UPLOAD_ATTACHMENT_MESSAGE, $"9{header}.{num}c."), "UploadDocumentHighRisk",
                                        new List<PairMemberValue>() { new PairMemberValue { Member = item.IsLegalEntityHighRisk },
                                                                  new PairMemberValue { Member = item.DoesEntityProvideEvidence }}));

                                    //high risk add warning message
                                    if (item.IsLegalEntityHighRisk.HasValue && item.IsLegalEntityHighRisk.Value)
                                    {
                                        if (economicSubstanceDeclarationDto.FiscalEndDate.IsLessThan(EssConstants.DeclarationChanges_2_0_Date))
                                        {
                                            var validationIPTotalGrossAnnualIncomeWarning = item.TotalGrossAnnualIncome.IsNullOrEmpty(string.Format(ValidationMessages.ISRECOMEND_MESSAGE, $"9{header}.{num}d."), nameof(item.TotalGrossAnnualIncome));
                                            validationIPTotalGrossAnnualIncomeWarning.ValidationType = Bdo.Ess.Common.EconomicSubstance.ValidationType.Warning;
                                            resultvalidation.Add(validationIPTotalGrossAnnualIncomeWarning);
                                        }

                                        List<ValidationResultDto> validationWarrning = new List<ValidationResultDto>()
                                            {
                                                item.GrossIncomeRoyalities.IsNullOrEmpty(string.Format(ValidationMessages.ISRECOMEND_MESSAGE, $"9{header}.{num}e."), nameof(item.GrossIncomeRoyalities)),
                                                item.GrossIncomeGains.IsNullOrEmpty(string.Format(ValidationMessages.ISRECOMEND_MESSAGE, $"9{header}.{num}f."), nameof(item.GrossIncomeGains))
                                            };

                                        validationWarrning.ToList().ForEach(n => n.ValidationType = ValidationType.Warning);
                                        resultvalidation.AddRange(validationWarrning);
                                    }
                                }

                                //BVI Declaration Changes 2.0
                                if ((item.IsLegalEntityHighRisk.HasValue && item.IsLegalEntityHighRisk.Value) && economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                {
                                    var validationGrossIncomeOthersWarning = item.GrossIncomeOthers.IsNullOrEmpty(string.Format(ValidationMessages.ISRECOMEND_MESSAGE, $"9{header}.{num}g."), nameof(item.GrossIncomeOthers));
                                    validationGrossIncomeOthersWarning.ValidationType = Bdo.Ess.Common.EconomicSubstance.ValidationType.Warning;
                                    resultvalidation.Add(validationGrossIncomeOthersWarning);
                                    resultvalidation.Add(item.TangibleAsset.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}h."), nameof(item.TangibleAsset)));
                                    resultvalidation.Add(item.TangibleAsset.IsMaxLength(string.Format(ValidationMessages.VALID_LENGTH_MESSAGE, 255, $"9{header}.{num}h."), nameof(item.TangibleAsset), 255));
                                    resultvalidation.Add(item.TangibleAssetIncome.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}i"), nameof(item.TangibleAssetIncome)));
                                    resultvalidation.Add(item.TangibleAssetEmployeeResponsibility.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}j."), nameof(item.TangibleAssetEmployeeResponsibility)));
                                    resultvalidation.Add(item.HistoryofStrategicDecisionsInBVI.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}k."), nameof(item.HistoryofStrategicDecisionsInBVI)));
                                    resultvalidation.Add(item.HistoryofTradingActivityIncome.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}l."), nameof(item.HistoryofTradingActivityIncome)));
                                }
                            }

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 7, economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                // other CIGA
                                num++;
                                if (!item.IsLegalEntityHighRisk.HasValue || !item.IsLegalEntityHighRisk.Value)
                                {
                                    resultvalidation.Add(item.DoesLegalEntityConductCIGA.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.DoesLegalEntityConductCIGA)));
                                    resultvalidation.Add(item.DoesEntityProvideEvidenceroRebut.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}b."), nameof(item.DoesEntityProvideEvidenceroRebut),
                                        new List<PairMemberValue>() { new PairMemberValue { Member = item.DoesLegalEntityConductCIGA } }));
                                    resultvalidation.Add(item.OtherCIGADocuments.AnyUploadedDocument(String.Format(ValidationMessages.UPLOAD_ATTACHMENT_MESSAGE, $"9{header}.{num}c."), "UploadOtherCIGA",
                                        new List<PairMemberValue>() { new PairMemberValue { Member = item.DoesLegalEntityConductCIGA },
                                                                  new PairMemberValue { Member = item.DoesEntityProvideEvidenceroRebut }}));
                                }

                                //BVI Declaration Changes 2.0
                                if ((item.DoesLegalEntityConductCIGA.HasValue && item.DoesLegalEntityConductCIGA.Value) && economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                {
                                    resultvalidation.Add(item.RelevantIPAsset.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}d."), nameof(item.RelevantIPAsset)));
                                    resultvalidation.Add(item.RelevantIPAsset.IsMaxLength(string.Format(ValidationMessages.VALID_LENGTH_MESSAGE, 255, $"9{header}.{num}d."), nameof(item.RelevantIPAsset), 255));
                                    resultvalidation.Add(item.IPAssetsInBVI.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}e."), nameof(item.IPAssetsInBVI)));
                                    resultvalidation.Add(item.IPAssetsEmployeeResponsibility.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}f."), nameof(item.IPAssetsEmployeeResponsibility)));
                                    resultvalidation.Add(item.ConcreteEvidenceDecisionInBVI.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}g."), nameof(item.ConcreteEvidenceDecisionInBVI)));
                                }
                            }

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 8, economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                // Income generation activities
                                // exception for Intellectulaul
                                num++;

                                if ((item.ReleventActivityValue == RelevantActivity.IntelBA &&
                                   item.IsLegalEntityHighRisk.HasValue && !item.IsLegalEntityHighRisk.Value
                                   && item.DoesLegalEntityConductCIGA.HasValue && !item.DoesLegalEntityConductCIGA.Value) ||

                                       item.ReleventActivityValue != RelevantActivity.IntelBA)
                                {
                                    resultvalidation.Add(item.ConductedCIGAActivity?.IsOtherSelected($"Other Option is required.", nameof(item.CIGAOtherDetail), item.CIGAOtherDetail));
                                    if (item.ConductedCIGAActivity?.Where(x => !x.IsDeleted && x.CIGAActivity.ActivityDetails.Contains("Other")).Count() > 0 && !string.IsNullOrEmpty(item.CIGAOtherDetail))
                                    {
                                        resultvalidation.Add(item.CIGAOtherDetail.IsMaxLength(string.Format(ValidationMessages.VALID_LENGTH_MESSAGE, 100, $"9{header}.{num}a."), nameof(item.CIGAOtherDetail), 100));
                                    }

                                    //BVI Declaration Changes 2.0
                                    if (economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                    {
                                        resultvalidation.Add(item.ConductedCIGAActivity.ValidateConductedCIGAActivity($"The user should add CIGA activity.", nameof(item.ConductedCIGAActivity)));
                                    }
                                }
                            }

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 9, economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                // Outsourcing
                                // exception for Intellectulaul
                                num++;

                                if ((item.ReleventActivityValue == RelevantActivity.IntelBA && item.IsLegalEntityHighRisk.HasValue && !item.IsLegalEntityHighRisk.Value
                                    && item.DoesLegalEntityConductCIGA.HasValue && !item.DoesLegalEntityConductCIGA.Value) ||

                                        item.ReleventActivityValue != RelevantActivity.IntelBA)
                                {

                                    resultvalidation.Add(item.HasAnyIncomeBeenOutsourced.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}a."),
                                     nameof(item.HasAnyIncomeBeenOutsourced)));
                                    resultvalidation.AddRange(ValidationHelper.ValidateOutsourceDetail(item.ServiceProviders, string.Format(ValidationMessages.DETAIL_REQUIRED_MESSAGE, $"9{header}.{num}b."), new List<PairMemberValue>() { new PairMemberValue { Member = item.HasAnyIncomeBeenOutsourced } }));

                                }

                                //BVI Declaration Changes 2.0
                                if (economicSubstanceDeclarationDto.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                                {
                                    resultvalidation.Add(item.TotalExpenditureIncurredInBVI.IsNullorNoValue(string.Format(ValidationMessages.VALUE_REQUIRED_MESSAGE, $"9{header}.{num}c."), nameof(item.TotalExpenditureIncurredInBVI)));
                                }
                            }

                            if (ValidationHelper.GetShouldValidate(item.ReleventActivityValue, 10, economicSubstanceDeclarationDto.FiscalEndDate))
                            {
                                // equipment
                                num++;
                                resultvalidation.Add(item.EquipmentDescription.IsNullOrEmpty(String.Format(ValidationMessages.ISREQUIRED_MESSAGE, $"9{header}.{num}a."), nameof(item.EquipmentDescription)));

                            }
                            // end
                            validationResultMainDto.ValidationResultDto = resultvalidation.Where(x => x != null && !string.IsNullOrEmpty(x.FieldName)).ToList();
                            validationResult.Add(validationResultMainDto);

                            header++;
                        }
                        break;
                    }

                case 5:
                    {
                        ValidationResultMainDto validationResultMainDto = new ValidationResultMainDto();
                        validationResultMainDto.ActivityName = "Step5";

                        resultvalidation.Add(economicSubstanceDeclarationDto.SupportingComments.IsMaxLength(String.Format(ValidationMessages.VALID_LENGTH_MESSAGE, 510, "10a."), nameof(economicSubstanceDeclarationDto.SupportingComments), 510));
                        resultvalidation = resultvalidation.Where(x => !string.IsNullOrEmpty(x.FieldName)).Distinct().ToList();

                        validationResultMainDto.ValidationResultDto = resultvalidation.Where(x => !string.IsNullOrEmpty(x.FieldName)).ToList();
                        validationResult.Add(validationResultMainDto);
                        break;
                    }

            }
            return validationResult;
        }



        public async Task<List<ValidationResultMainDto>> SingleStepValidation(EconomicSubstanceStepDto input)
        {
            return await ValidationRules(input);

        }


        public async Task<List<ValidationResultMainDto>> AllStepValidation(EconomicSubstanceDeclarationDto input)
        {

            VaildationEconomicInput inputValidation = new VaildationEconomicInput();

            var countries = _lookupMainMicroservice.Call(x => x.GetAllCountries(NoInput.Input)).Result;
            var countryDict = countries.ToDictionary(x => x.CountryCode, x => x);

            var currencies = _lookupMainMicroservice.Call(x => x.GetAllCurrencies(new SingleFieldInput<string>())).Result;
            var curencyDict = currencies.ToDictionary(x => x.CurrencyCode, x => x);


            inputValidation.countryDict = countryDict;
            inputValidation.curencyDict = curencyDict;
            inputValidation.economicSubstanceDeclarationDto = RePopulateESData(input);




            return await ValidationImportRules(inputValidation);

        }

        private IList<ValidationResultDto> ValidateEntityDetails(EsEntityDetailDto item)
        {
            List<ValidationResultDto> resultvalidation = new List<ValidationResultDto>();

            var validationTINWarning = item.IdentificationNumber.IsNullOrEmpty(string.Format("You must enter the TIN# if it exists {0}", $"6f."), nameof(item.IdentificationNumber));
            validationTINWarning.ValidationType = Bdo.Ess.Common.EconomicSubstance.ValidationType.Warning;
            resultvalidation.Add(validationTINWarning);

            resultvalidation.Add(item.TotalGrossAnnualIncomeString.IsDecimal(string.Format(ValidationMessages.VALID_TYPE_MESSAGE, $"6g."), "TotalGrossAnnualIncomeE", 15, 2));
            var validationTotalGrossAnnualIncomeWarning = item.TotalGrossAnnualIncomeString.IsNullOrEmpty(string.Format(ValidationMessages.ISRECOMEND_MESSAGE, $"6g."), nameof(item.TotalGrossAnnualIncome));
            validationTotalGrossAnnualIncomeWarning.ValidationType = Bdo.Ess.Common.EconomicSubstance.ValidationType.Warning;
            resultvalidation.Add(validationTotalGrossAnnualIncomeWarning);

            resultvalidation.Add(item.IsSameAsRegisteredAddressString.IsBoolean(string.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, "6h."), nameof(item.IsSameAsRegisteredAddress)));
            resultvalidation.Add(item.IsSameAsRegisteredAddress.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, "6h."), nameof(item.IsSameAsRegisteredAddress)));
            if (item.IsSameAsRegisteredAddress.HasValue && !item.IsSameAsRegisteredAddress.Value)
            {
                resultvalidation.Add(item.AddressLine1.IsNullOrEmpty(string.Format("Response for Address Line 1 is required {0}", $"6h.1"), nameof(item.AddressLine1)));
                resultvalidation.Add(item.Country.IsCountryNullOrNA(string.Format("Response for Country is required {0}", $"6h.1."), nameof(item.Country)));
            }

            var validationMNEWarning = item.MNEGroupName.IsNullOrEmpty(string.Format(ValidationMessages.ISRECOMEND_MESSAGE, "6i."), nameof(item.MNEGroupName));
            validationMNEWarning.ValidationType = Bdo.Ess.Common.EconomicSubstance.ValidationType.Warning;
            resultvalidation.Add(validationMNEWarning);

            resultvalidation.Add(item.DoesEntityHaveUltimateParentString.IsBoolean(string.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, "6j."), nameof(item.DoesEntityHaveUltimateParent)));
            resultvalidation.Add(item.DoesEntityHaveUltimateParent.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, "6j."), nameof(item.DoesEntityHaveUltimateParent)));

            if (item.DoesEntityHaveUltimateParent.HasValue && item.DoesEntityHaveUltimateParent.Value)
            {
                resultvalidation.Add(ValidationHelper.EntityAdditionalDetail(item.EsEntityAdditionalDetails.Where(i => i.EntityType == ParentEntityType.Ultimate).ToList(), string.Format(ValidationMessages.DETAIL_REQUIRED_MESSAGE, "6j.1"), "UltimateParent"));
            }

            resultvalidation.Add(item.DoesEntityHaveImmediateParentString.IsBoolean(string.Format(ValidationMessages.INVALID_BOOLEAN_MESSAGE, "6k."), nameof(item.DoesEntityHaveImmediateParent)));
            resultvalidation.Add(item.DoesEntityHaveImmediateParent.IsNullOrEmpty(string.Format(ValidationMessages.ISREQUIRED_MESSAGE, "6k."), nameof(item.DoesEntityHaveImmediateParent)));

            if (item.DoesEntityHaveImmediateParent.HasValue && item.DoesEntityHaveImmediateParent.Value)
            {
                resultvalidation.Add(ValidationHelper.EntityAdditionalDetail(item.EsEntityAdditionalDetails.Where(i => i.EntityType == ParentEntityType.Immediate).ToList(), string.Format(ValidationMessages.DETAIL_REQUIRED_MESSAGE, "6k.1"), "ImmediateParent"));
            }
            return resultvalidation;
        }


        private EconomicSubstanceDeclarationDto RePopulateESData(EconomicSubstanceDeclarationDto input)
        {

            input = input.FixDateTime();
            input.IsFinaincialCanChangeString = input.IsFinaincialCanChange.BooleanToString();
            input.DoesEntityHaveParentEntityString = input.DoesEntityHaveParentEntity.BooleanToString();
            input.DoesEntityMakeClaimOutisedBVIString = input.DoesEntityMakeClaimOutisedBVI.BooleanToString();
            input.IsEvidenceNonResidenceOrTreatmentString = input.IsEvidenceNonResidenceOrTreatment.BooleanToString();
            input.JurisdictionTaxResidentString = input?.JurisdictionTaxResident?.CountryCode ?? string.Empty;
            input.ParentJurisdictionString = input?.ParentJurisdiction?.CountryCode ?? string.Empty;
            input.IsEvidenceNonResidenceOrTreatmentString = input.IsEvidenceNonResidenceOrTreatment.BooleanToString();
            input.CurrencyString = input?.Currency?.CurrencyCode ?? string.Empty;
            foreach (var activity in input.RelevantActivities.Where(x => x.IsChecked && x.RelevantActivityName != "None"))
            {
                activity.ReleventActivityValueString = activity.ReleventActivityValue.GetAmbientValue();
                activity.IsCarriedForPartFinancialPeriodString = activity.IsCarriedForPartFinancialPeriod.BooleanToString();
                activity.IsActivityDirectedInBVIString = activity.IsActivityDirectedInBVI.BooleanToString();
                activity.IsMeetingMinutesInBVIString = activity.IsMeetingMinutesInBVI.BooleanToString();
                activity.HasAnyIncomeBeenOutsourcedString = activity.HasAnyIncomeBeenOutsourced.BooleanToString();
                activity.DoesEntityComplyItsStatutoryObString = activity.DoesEntityComplyItsStatutoryObligations.BooleanToString();
                activity.DoesEntityManageEquityString = activity.DoesEntityManageEquity.BooleanToString();
                activity.IsLegalEntityHighRiskString = activity.IsLegalEntityHighRisk.BooleanToString();
                activity.DoesEntityProvideEvidenceString = activity.DoesEntityProvideEvidence.BooleanToString();
                activity.DoesLegalEntityConductCIGAString = activity.DoesLegalEntityConductCIGA.BooleanToString();
                activity.DoesEntityProvideEvidenceroRString = activity.DoesEntityProvideEvidenceroRebut.BooleanToString();
                activity.ServiceProviders?.ForEach(x => { x.NumberOfStaffEmployedString = x.NumberOfStaffEmployed?.ToString(); x.HoursPerMonthForEmployeeString = x.HoursPerMonthForEmployee?.ToString(); x.IsEntityAbleToMonitorString = x.IsEntityAbleToMonitor.BooleanToString(); });
                activity.ManagementDetails?.ForEach(x => { x.IsResidentInBVIString = x.IsResidentInBVI.BooleanToString(); });
                activity.AttendMeetingDetails?.ForEach(x => { x.IsPhysicallyPresentString = x.IsPhysicallyPresent.BooleanToString(); });
                activity.IsQuorumBoardMeetingInBVIString = activity.IsQuorumBoardMeetingInBVI.BooleanToString();
            }
            return input;
        }

        public async Task<ESFiscalYearDto> GetFiscalYear(GetEconomicSubstanceByEntityIdDto input)
        {
            var repo = GetRepository<EconomicSubstanceDeclaration>();

            ESFiscalYearDto esFiscalYearDto = new ESFiscalYearDto();
            esFiscalYearDto.HasValue = false;
            var result = await repo.Query(x => x.CorporateEntityId == input.Id && !x.IsDeleted).OrderByDescending(x => x.FiscalStartDate).ToListAsync();

            if (result.Count > 0)
            {
                esFiscalYearDto.HasValue = true;
                esFiscalYearDto.FiscalStartDate = result[0].FiscalStartDate.AddYears(1);
                esFiscalYearDto.FiscalEndDate = result[0].FiscalEndDate.AddYears(1);
            }

            return esFiscalYearDto;
        }

        public async Task<List<EconomicSubstanceDeclarationDto>> GetEconomicSubstances(GetEconomicSubstancesInput input)
        {
            var repo = GetRepository<EconomicSubstanceDeclaration>();
            var items = await repo.Query().ToListAsync();
            await GetlookUpTableValueFromMicroService(Guid.Empty);
            var result = items.MapToEconomicSubstanceDtoList(false);
            return result;

        }
        public async Task<List<EconomicSubstanceDeclarationDto>> GetEconomicSubstancesByIds(Guid[] ids)
        {
            using (Logger.BeginScope(new Dictionary<string, object>
            {
                { "CspNumber", GetSessionValue<string>(EssSessionKeys.CtspNumber) }
            }))
            {
                try
                {
                    var repo = GetRepository<EconomicSubstanceDeclaration>();
                    var items = await repo.Query(x => ids.Contains(x.CorporateEntityId)).ToListAsync();
                    await GetlookUpTableValueFromMicroService(Guid.Empty);
                    var result = items.MapToEconomicSubstanceDtoList(false);
                    return result;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "GetEconomicSubstancesByIds encountered an exception.");
                    throw;
                }
            }
        }


        private Expression<Func<EconomicSubstanceDeclaration, bool>> GetExpressionFromCriteria(SearchExpression searchExpression)
        {
            throw new NotImplementedException();
        }

        private Expression GetExpression(SearchExpression searchExpression, ParameterExpression param)
        {
            if (searchExpression.__TypeName.Equals("Basic", StringComparison.OrdinalIgnoreCase))
            {
                return GetBasicExpression(param, searchExpression.Field, searchExpression.SearchMethod, searchExpression.Value);
            }
            else if (searchExpression.__TypeName.Equals("Advanced", StringComparison.OrdinalIgnoreCase))
            {
                var left = GetExpression(searchExpression.LeftExpression, param);
                var right = GetExpression(searchExpression.RightExpression, param);
                if (searchExpression.Connector == Common.Search.ExpressionConnector.AndAlso)
                {
                    return Expression.AndAlso(left, right);
                }

                return Expression.OrElse(left, right);
            }

            return GetExpression(searchExpression.Expression, param);
        }

        private Expression GetBasicExpression(ParameterExpression param, SearchableFieldId field, SearchMethod searchMethod, string value)
        {
            return Expression.Constant(value);
        }

        public async Task<int> GetEconomicSubstancesCount(GetEconomicSubstancesInput input)
        {
            var repo = GetRepository<EconomicSubstanceDeclaration>();
            var expression = GetExpressionFromCriteria(input.Criteria.SearchExpression);
            var count = await repo.Query(expression).CountAsync();
            return count;
        }

        public async Task<List<EconomicSubstanceDeclarationDto>> GetEconomicSubstanceByEntityID2(GetEconomicSubstanceByEntityIdDto input)
        {
            Logger.LogInformation("GetEconomicSubstanceByEntityID2 processing {CorporateEntityId}", input.Id);
            var repo = GetRepository<EconomicSubstanceDeclaration>();
            var items = await repo.Query().Where(x => x.CorporateEntityId == input.Id).ToListAsync();
            Logger.LogInformation("GetEconomicSubstanceByEntityID2 completed");
            return Mapper.Map<List<EconomicSubstanceDeclarationDto>>(items);
        }

        public async Task<List<EconomicSubstanceDeclarationDto>> CaGetEconomicSubstanceByEntityID(CaGetEconomicSubstanceByEntityIdDto input)
        {
            return await GetEconomicSubstanceByEntityIDInternal(input.Id, true);
        }

        [Obsolete("This method doesn't do anything. Just remove it!")]
        public async Task<List<EconomicSubstanceDeclarationDto>> SearchRandomSamples(EssRandomSamplesInput input)
        {
            return null;
        }

        private async Task<List<EconomicSubstanceDeclarationDto>> CaGetEconomicSubstances()
        {
            var repo = GetRepository<EconomicSubstanceDeclaration>();
            var items = await repo.Query().Where(x => x.Status == EconomicSubstanceStatus.Submitted).ToListAsync();
            return Mapper.Map<List<EconomicSubstanceDeclarationDto>>(items);
        }

        public async Task<List<EconomicSubstanceDeclarationDto>> CaGetEconomicSubstancesWithDetails(GetEconomicSubstancesWithDetailsInput input)
        {
            var o = CaGetEconomicSubstances().ToObservable().SelectMany(x =>
            {
                return x.ToObservable().SelectMany(y =>
                {
                    return GetEconomicSubstanceDetailById(new GetEconomicSubstanceByEntityDetailIdDto() { Id = y.Id }).ToObservable();
                });
            }).ToList();

            var result = await o;

            return new List<EconomicSubstanceDeclarationDto>(result);
        }

        [Obsolete("This method doesn't do anything. Just remove it! It always returns null!")]
        public async Task<List<EconomicSubstanceDeclarationDto>> GetNonResidentEconomicSubstances(GetNonResidentEconomicSubstancesInput input)
        {
            return null;
        }

        private IQueryable<EconomicSubstanceDeclaration> NonResidentDeclarationsQuery(DateTime startDate, DateTime endDate)
        {
            var repo = GetRepository<EconomicSubstanceDeclaration>();
            var query = repo.Query().
                Where(x => !x.IsDeleted).
                Where(x => x.Status == EconomicSubstanceStatus.Submitted).
                // Where(x => !(x.IsEntityResidentCompInBRB ?? false) && (x.IsEntityTaxResidentInJusOutBRB ?? false)).
                Where(x => x.SubmissionDate >= startDate && x.SubmissionDate < endDate);
            return query;
        }

        public async Task<GetNonResidentCorporateEntityIdsOutput> GetNonResidentCorporateEntityIds(GetNonResidentCorporateEntityIdsInput input)
        {
            // lookups
            var getCountriesInput = new SingleFieldInput<string>();
            var countries = await _lookupMainMicroservice.Call(x => x.GetAllCountriesIncludeNon(getCountriesInput));
            var countryDict = countries.ToDictionary(x => x.Id);

            var entityIds = await NonResidentDeclarationsQuery(input.StartDate, input.EndDate).ToObservable().
                Where(x => x.JurisdictionTaxResidentId != null && countryDict[x.JurisdictionTaxResidentId.Value].CountryCode == input.CountryCode).
                GroupBy(x => x.CorporateEntityId).
                Select(x => x.Key).ToArray();

            return new GetNonResidentCorporateEntityIdsOutput() { Ids = entityIds };
        }

        public async Task<List<EconomicSubstanceDeclarationDto>> SearchNonResidentDeclarations(SearchNonResidentDeclarationsInput input)
        {
            // lookups
            var getCountriesInput = new SingleFieldInput<string>();
            var countries = await _lookupMainMicroservice.Call(x => x.GetAllCountriesIncludeNon(getCountriesInput));
            var countryDict = countries.ToDictionary(x => x.Id);

            var result = await NonResidentDeclarationsQuery(input.StartDate, input.EndDate).
                Decorate(x => input.EntityId == null ? x : x.Where(y => y.CorporateEntityId == input.EntityId.Value)).
                Decorate(x =>
                {
                    return x;
                }).
                ToObservable().
                Where(x => input.CountryCode == null || (x.JurisdictionTaxResidentId != null && countryDict[x.JurisdictionTaxResidentId.Value].CountryCode == input.CountryCode)).
                SelectMany(x =>
                {
                    var dto = Mapper.Map<EconomicSubstanceDeclarationDto>(x);
                    if (!input.IncludeEntity)
                    {
                        return Observable.Return(dto);
                    }
                    var getEntityInput = new GetEntityInput() { Id = x.CorporateEntityId };
                    return _corporateEntityMicroservice.Call(x => x.GetEntity(getEntityInput)).ToObservable().Select(y =>
                    {
                        dto.CorporateEntity = y;
                        return dto;
                    });
                }).
                ToArray();

            var esdto = Mapper.Map<List<EconomicSubstanceDeclarationDto>>(result);

            return esdto;
        }


        public async Task<EconomicSubstanceDeclarationDto> GetBasicEconomicSubstanceByID(SingleFieldInput<Guid> input)
        {
            var repo = GetRepository<EconomicSubstanceDeclaration>();

            var item = await repo.Query(x => x.Id == input.Value).Include(x => x.EsEntityDetails).ThenInclude(x => x.EsEntityAdditionalDetails).FirstOrDefaultAsync();


            return item.MapToEconomicSubstanceDto();

        }

        public async Task<List<EconomicSubstanceDeclarationDto>> SearchNotStartedFilings(SearchNotStartedFilingsInput input)
        {
            var entityStatuses = await _lookupMainMicroservice.Call(x => x.GetEntityStatuses(NoInput.Input));
            var entityStatusDict = entityStatuses.ToDictionary(x => x.Id);

            var repo = GetRepository<EconomicSubstanceDeclaration>();

            var result = await repo.Query().
                Where(x => x.Status == EconomicSubstanceStatus.Submitted).
                Where(x => x.FiscalEndDate.Year == input.Year).
                ToObservable().SelectMany(decl =>
                {
                    var getStatusInput = new SingleFieldInput<Guid> { Value = decl.Id };
                    return _workflowMicroservice.Call(x => x.GetESReviewStatusByESID(getStatusInput)).ToObservable().SelectMany(status =>
                    {
                        if (status.Code == 0 || status.Code == (int)EconomicSubstanceReviewStatus.NotStarted)
                        {
                            var dto = Mapper.Map<EconomicSubstanceDeclarationDto>(decl);

                            return Observable.Return(dto);
                        }
                        return Observable.Empty<EconomicSubstanceDeclarationDto>();
                    }).SelectMany(x =>
                    {
                        var getEntityInput = new GetEntityInput() { Id = x.CorporateEntityId };
                        return _corporateEntityMicroservice.Call(x => x.GetEntity(getEntityInput)).ToObservable().SelectMany(y =>
                        {
                            if (!entityStatusDict[y.StatusId].RequireDeclaration || x.IsDeleted)
                            {
                                return Observable.Empty<EconomicSubstanceDeclarationDto>();
                            }
                            x.CorporateEntity = y;
                            return Observable.Return(x);
                        });
                    });
                }).ToList();

            return Mapper.Map<List<EconomicSubstanceDeclarationDto>>(result);
        }


        private void GetDefaultFiscalYears(Guid id, EconomicSubstanceDeclarationDto economicSubstanceDeclaration)
        {

            var repo = GetRepository<EconomicSubstanceDeclaration>();
            var result = repo.Query(x => x.CorporateEntityId == id && !x.IsDeleted && x.Id != economicSubstanceDeclaration.Id).Include(x => x.EsEntityDetails).ThenInclude(x => x.EsEntityAdditionalDetails).OrderByDescending(x => x.FiscalStartDate).ToList();

            if (result.Count == 0)
            {
                // need to get the coperation entity Date of Incoperation 
                var entityId = new GetEntityInput { Id = id };
                var corporateEntity = _corporateEntityMicroservice.Call(x => x.GetEntity(entityId)).Result;
                var dateOfInco = corporateEntity.IncorporationDate;
                economicSubstanceDeclaration.DefaultStartDate = (dateOfInco.Value > new DateTime(2019, 1, 1)) ? dateOfInco.Value : new DateTime(2019, 6, 30);

            }
            else
            {
                economicSubstanceDeclaration.DefaultStartDate = result[0].FiscalStartDate.AddYears(1);

            }

            economicSubstanceDeclaration.DefaultEndDate = economicSubstanceDeclaration.DefaultStartDate.AddYears(1);
            economicSubstanceDeclaration.DefaultEndDate = economicSubstanceDeclaration.DefaultEndDate.Subtract(TimeSpan.FromDays(1));


            economicSubstanceDeclaration.DefaultStartDateString = economicSubstanceDeclaration.DefaultStartDate.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
            economicSubstanceDeclaration.DefaultEndDateString = economicSubstanceDeclaration.DefaultEndDate.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);



        }
        public async Task<EconomicSubstanceDeclarationDto> GetInitialRelevantActivity(SingleFieldInput<Guid> input)
        {
            using (Logger.BeginScope(new Dictionary<string, object>
            {
                { "CorporateEntityId", input.Value },
                { "CspNumber", GetSessionValue<string>(EssSessionKeys.CtspNumber) },
            }))
            {
                try
                {
                    // first check if the entity has a pervious declared ESS
                    EconomicSubstanceDeclarationDto economicSubstanceDeclaration = new EconomicSubstanceDeclarationDto();

                    GetDefaultFiscalYears(input.Value, economicSubstanceDeclaration);

                    economicSubstanceDeclaration.RelevantActivities = EconomicSubstanceMapping.AddMissingRelevantActivity(economicSubstanceDeclaration.RelevantActivities);
                    economicSubstanceDeclaration.CorporateEntityId = input.Value;
                    return economicSubstanceDeclaration;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "GetInitialRelevantActivity encountered an exception.");
                    throw;
                }
            }
        }
        public async Task<bool> CheckFilingExists(CheckFilingExistsInput input)
        {
            var repo = GetRepository<EconomicSubstanceDeclaration>();
            var count = await repo.Query().
                Where(x => !x.IsDeleted).
                Where(x => x.Status == EconomicSubstanceStatus.Submitted).
                Where(x => x.CorporateEntityId == input.EntityId).
                Where(x => x.FiscalEndDate.Year == input.Year).
                CountAsync();
            return count > 0;
        }

        public async Task<CanHardDeleteEntityOutput> EntityHasAnyDeclarations(CanHardDeleteEntityInput input)
        {

            var entityId = new GetEntityByIdDto { Id = input.BosssEntityId.ToString(), IsIdExternalReference = true };
            var corporateEntity = await _corporateEntityMicroservice.Call(x => x.GetEntityByID(entityId));


            var repo = GetRepository<EconomicSubstanceDeclaration>();
            var declaration = await repo.Query(x => x.CorporateEntityId == corporateEntity.Id).Where(x => !x.IsDeleted).ToListAsync();

            return (declaration.Count > 0) ? new CanHardDeleteEntityOutput { CanBeDeleted = false } : new CanHardDeleteEntityOutput { CanBeDeleted = true };

        }

        public async Task<DeclarationPeriodDto[]> GetDeclarationPeriods(Guid[] input)
        {
            var repo = GetRepository<EconomicSubstanceDeclaration>();

            var declarations = await repo
                .Query(x => input.Contains(x.CorporateEntityId) && !x.IsDeleted)
                .Select(x => new
                {
                    x.Id,
                    x.CorporateEntityId,
                    x.FiscalStartDate,
                    x.FiscalEndDate,
                    x.Status,
                })
                .ToArrayAsync();

            return declarations
                .Select(x => new DeclarationPeriodDto
                {
                    DeclarationId = x.Id,
                    EntityId = x.CorporateEntityId,
                    FiscalPeriodStart = x.FiscalStartDate,
                    FiscalPeriodEnd = x.FiscalEndDate,
                    IsSubmitted = x.Status == EconomicSubstanceStatus.Submitted
                        || x.Status == EconomicSubstanceStatus.ReSubmitted,
                })
                .ToArray();
        }



        public async Task<EconomicSubstanceDeclarationDto> ReopenEconomicSubstance(GetEconomicSubstanceByEntityIdDto input)
        {
            var result = await GetEconomicSubstanceDetailById(new GetEconomicSubstanceByEntityDetailIdDto { Id = input.Id });
            if (result.Status != EconomicSubstanceStatus.Submitted && result.Status != EconomicSubstanceStatus.ReSubmitted)
                return new EconomicSubstanceDeclarationDto();
            var repo = GetRepository<EconomicSubstanceDeclaration>();
            await CreateHistoryEconomicSubstance(result);
            result.Status = EconomicSubstanceStatus.ReOpen;

            var resultConverted = result.MapToEconomicSubstance();
            resultConverted.Synced = false;
            repo.Update(resultConverted);

            await CommitAsync();

            SingleFieldInput<Guid> assigmentInput = new SingleFieldInput<Guid>();
            assigmentInput.Value = input.Id;
            await _caWorkflowService.Call(x => x.DeleteAssignmentItem(assigmentInput));

            // need also to delete the red flag from the redflag result

            await _redFlagMicroservice.Call(x => x.DeleteRedFlagForReopenDeclarations(assigmentInput));

            await _informationExchangeMicroservice.Call(x => x.UpdateESInformationDclartionStatus(input.Id));

            return await GetEconomicSubstanceDetailById(new GetEconomicSubstanceByEntityDetailIdDto { Id = result.Id });

        }
        public async Task<bool> RequestToReopenEconomicSubstance(AmendmentRequest input)
        {
            var result = await GetEconomicSubstanceDetailById(new GetEconomicSubstanceByEntityDetailIdDto { Id = input.EconomicSubstanceId });
            if (result.Status != EconomicSubstanceStatus.Submitted && result.Status != EconomicSubstanceStatus.ReSubmitted)
                return false;
            var repo = GetRepository<EconomicSubstanceDeclaration>();
            var resultConverted = result.MapToEconomicSubstance();
            resultConverted.Synced = false;
            resultConverted.IsEmailSentToReopen = true; // Set IsEmailSentToReopen to true
            repo.Update(resultConverted);
            await CommitAsync();

            return true;

        }


        public async Task<bool> UpdateEconomicSubstanceAssessment(EconomicSubstanceAssessmentInput input)
        {
            try
            {
                var repo = GetRepository<EconomicSubstanceDeclaration>();
                var result = repo.Query(x => x.Id == input.Id).FirstOrDefault();
                if (!string.IsNullOrEmpty(input.PrTreatmentDueDateString))
                {
                    result.ProvisionalTreatmentDueDate = input.PrTreatmentDueDateString.ConvertToDateTimeNullable();
                }
                result.ApproveRejectProvisionalTreatment = input.ApproveRejectProvisionalTreatment;

                repo.Update(result);

                await CommitAsync();

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateESAssessmentRequestInformation(EconomicSubstanceAssessmentInput input)
        {

            Logger.LogDebug("In Information Requested ");

            try
            {
                var repo = GetRepository<EconomicSubstanceDeclaration>();
                var repo2 = GetRepository<EconomicSubstanceInformationRequired>();
                var result = repo.Query(x => x.Id == input.Id).FirstOrDefault();


                EconomicSubstanceInformationRequired econmoicSubstanceInfoRequired = new EconomicSubstanceInformationRequired();
                Logger.LogDebug($"In Information Requested IgnoreInformationRequiredFromCA value =  {input.IgnoreInformationRequiredFromCA}");
                if (!input.IgnoreInformationRequiredFromCA)
                {

                    Logger.LogDebug($"Adding the Information Request data ");
                    if (!string.IsNullOrEmpty(input.InRequestedDueDateString))
                    {

                        econmoicSubstanceInfoRequired.InformationRequestedDueDate = input.InRequestedDueDateString.ConvertToDateTimeNullable();
                    }

                    Logger.LogDebug($"Adding the Information Request data InformationRequestedCommentsCA {input.InformationRequestedCommentsCA} ");

                    econmoicSubstanceInfoRequired.InformationRequestedCommentsCA = input.InformationRequestedCommentsCA;
                    econmoicSubstanceInfoRequired.EsInformationRequestDocumentCas =
                        Mapper.Map<List<EsInformationRequestDocumentCaDto>, List<EsInformationRequestDocumentCa>>(input.InformationRequestDocumentCas);

                    econmoicSubstanceInfoRequired.IsInformationRequired = true;


                    if (result.EconomicSubstanceInformationRequired == null)
                        result.EconomicSubstanceInformationRequired = new List<EconomicSubstanceInformationRequired>();

                    result.EconomicSubstanceInformationRequired.Add(econmoicSubstanceInfoRequired);
                }
                else
                {
                    Logger.LogDebug($"Adding the Information Request when IgnoreInformationRequiredFromCA = false ");
                    var resultRequiredInfo = await repo2
                             .Query().Where(x => x.EconomicSubstanceDeclarationId == input.Id)
                             .Include(x => x.EconomicSubstanceInformationRequiredDocuments)
                             .ThenInclude(x => x.Documents)
                             .ToListAsync();

                    result.EconomicSubstanceInformationRequired = resultRequiredInfo;

                    var latestRequiredInformation = result.EconomicSubstanceInformationRequired?.FirstOrDefault(x => x.IsInformationRequired.Value) ?? new EconomicSubstanceInformationRequired();

                    latestRequiredInformation.IsInformationRequired = false;

                }

                repo.Update(result);

                await CommitAsync();

                return true;
            }
            catch (Exception ex)
            {
                Logger.LogDebug($"Information Request when not done successfully {ex.Message}");
                throw ex;

            }
        }



        public async Task<ValidationResultDto> ValidateProvisionalTreatment(EconomicSubstanceDeclarationDto input)
        {
            // need to validate if there is any document 
            return input.ProvisionalTreatmentDocument.AnyUploadedDocument("attachments are required for submission", "ProvisionalTreatment", null);

        }


        public async Task<EconomicSubstanceDeclarationDto> SubmitProvisionalTreatment(EconomicSubstanceDeclarationDto input)
        {
            var userName = GetSessionValue<string>(EssSessionKeys.UserName);
            input.ProvisionalTreatmentSubmittedBy = userName;
            var repo = GetRepository<EconomicSubstanceDeclaration>();
            // need to get the exchange Rate

            var result = input.MapToEconomicSubstance();

            repo.Update(result);
            await CommitAsync();

            await _economicSubstanceEventProvisionalTreatmentAuditor.AuditProvisionalTreatment(result);
            var updatedES = await GetEconomicSubstanceDetailById(new GetEconomicSubstanceByEntityDetailIdDto { Id = input.Id });


            //1- Update the CA assessment table

            var esReviewStatusInput = new ESReviewStatusInputDto();
            var esReviewStatuses = await _lookupMainMicroservice.Call(x => x.GetESReviewStatus(esReviewStatusInput));
            var closedStatus = esReviewStatuses.FirstOrDefault(x => x.Code == (int)EconomicSubstanceReviewStatus.ReviewEvidence);

            ESAssignmentDto inputAssigment = new ESAssignmentDto();

            inputAssigment.EconomicSubstanceId = updatedES.Id;
            inputAssigment.AssessmentStatus = closedStatus.Name;
            inputAssigment.AssessmentStatusId = closedStatus.Id;

            await _caWorkflowService.Call(x => x.UpdateCaAssignmentESSInformationStatus(inputAssigment));

            // 2 Update the Ctsp Assessment table

            UpdateAssessmentStatusDto updateAssessmentStatusDto = new UpdateAssessmentStatusDto();
            updateAssessmentStatusDto.ReviewStatusId = closedStatus.Id;
            updateAssessmentStatusDto.DeclarationId = updatedES.Id;
            updateAssessmentStatusDto.SubmitterName = userName;
            updateAssessmentStatusDto.Comments = "Provisional Treatment Evidence submitted by CSP";
            await _workflowMicroservice.Call(x => x.UpdateAssessmentStatus(updateAssessmentStatusDto));

            return updatedES;

        }


        public async Task<ValidationResultDto> ValidateInformationRequired(EconomicSubstanceInformationRequiredDto input)
        {
            // if the user add comments then no need to validate the others == validation pass
            ValidationResultDto validation = new ValidationResultDto();
            if (!string.IsNullOrEmpty(input.InformationRequestedCommentsCTSP)) return validation;

            // need to validate if there is any document 
            validation = input.EconomicSubstanceInformationRequiredDocuments.AnyUploadedDocument("Comments or attachments are required for submission", "InformationRequired", null);
            // if there is documents no need to validate == validation pass
            if (string.IsNullOrEmpty(validation.FieldName)) return validation;

            // at this point no need to validate any more the first one fail and the second one
            validation.FieldName = "InformationRequired";
            validation.ValidationString = "Comments or attachments are required for submission";
            return validation;
        }





        public async Task<EconomicSubstanceDeclarationDto> SubmitInformationRequest(EconomicSubstanceInformationRequiredDto input)
        {
            var userName = GetSessionValue<string>(EssSessionKeys.UserName);
            var repo = GetRepository<EconomicSubstanceDeclaration>();

            var esDeclaration = await GetEconomicSubstanceById(new GetEconomicSubstanceByEntityIdDto { Id = input.EconomicSubstanceDeclarationId.Value });
            // update the current reuested information 
            var currentUpdatedInfo = esDeclaration.EconomicSubstanceInformationRequired.FirstOrDefault(x => x.Id == input.Id);

            currentUpdatedInfo.IsInformationRequired = false;
            currentUpdatedInfo.InformationRequestedSubmittedBy = userName;
            currentUpdatedInfo.InformationRequestedCommentsCTSP = input.InformationRequestedCommentsCTSP;
            currentUpdatedInfo.InformationRequestedSubmissionDate = input.InformationRequestedSubmissionDate;

            if (input.EconomicSubstanceInformationRequiredDocuments != null && input.EconomicSubstanceInformationRequiredDocuments.Count > 0)
            {
                if (currentUpdatedInfo.EconomicSubstanceInformationRequiredDocuments == null) currentUpdatedInfo.EconomicSubstanceInformationRequiredDocuments = new List<EconomicSubstanceInformationRequiredDocuments>();

                foreach (var item in input.EconomicSubstanceInformationRequiredDocuments)
                {
                    currentUpdatedInfo.EconomicSubstanceInformationRequiredDocuments.Add(new EconomicSubstanceInformationRequiredDocuments
                    {
                        FileName = item.FileName,
                        DocumentsId = item.DocumentsId,
                        ContetntType = item.ContetntType,
                        EconomicSubstanceInformationRequiredId = input.Id,
                        DocumentCategory = "InformationRequestedDocument"
                    });
                }
            }


            // need to update the following tables

            //1- Update the CA assessment table
            var esReviewStatusInput = new ESReviewStatusInputDto();
            var esReviewStatuses = await _lookupMainMicroservice.Call(x => x.GetESReviewStatus(esReviewStatusInput));
            var informationStatus = esReviewStatuses.FirstOrDefault(x => x.Code == (int)EconomicSubstanceReviewStatus.InformationReceived);
            ESAssignmentDto inputAssigment = new ESAssignmentDto();

            inputAssigment.EconomicSubstanceId = esDeclaration.Id;
            inputAssigment.AssessmentStatus = informationStatus.Name;
            inputAssigment.AssessmentStatusId = informationStatus.Id;

            await _caWorkflowService.Call(x => x.UpdateCaAssignmentESSInformationStatus(inputAssigment));

            // 2 Update the Ctsp Assessment table

            UpdateAssessmentStatusDto updateAssessmentStatusDto = new UpdateAssessmentStatusDto();
            updateAssessmentStatusDto.ReviewStatusId = informationStatus.Id;
            updateAssessmentStatusDto.DeclarationId = esDeclaration.Id;
            updateAssessmentStatusDto.SubmitterName = userName;
            updateAssessmentStatusDto.Comments = "Requested Information submitted by CSP";
            await _workflowMicroservice.Call(x => x.UpdateAssessmentStatus(updateAssessmentStatusDto));
            repo.Update(esDeclaration);

            await _economicSubstanceEventRequestedInformationAuditor.AuditInformationRequested(esDeclaration);

            await CommitAsync();
            var updatedES = await GetEconomicSubstanceDetailById(new GetEconomicSubstanceByEntityDetailIdDto { Id = input.EconomicSubstanceDeclarationId.Value });
            return updatedES;

        }

        private async Task<bool> CreateHistoryEconomicSubstance(EconomicSubstanceDeclarationDto input)
        {
            var saveOldDeclaration = input.ConvertObjectToCompressedJson();
            var repo = GetRepository<EconomicSubstanceDeclarationHistory>();
            EconomicSubstanceDeclarationHistory economicSubstanceDeclarationHistory = new EconomicSubstanceDeclarationHistory();
            economicSubstanceDeclarationHistory.EconomicSubstanceDeclarationObject = saveOldDeclaration;
            economicSubstanceDeclarationHistory.EconomicSubstanceDeclarationId = input.Id;
            economicSubstanceDeclarationHistory.Status = input.Status;
            economicSubstanceDeclarationHistory.OriginalSubmitterName = input.SubmitterName;
            economicSubstanceDeclarationHistory.OriginalSubmittionDate = input.SubmissionDate;
            repo.Add(economicSubstanceDeclarationHistory);
            await CommitAsync();
            return true;
        }



        public async Task<List<EconomicSubstanceDeclarationHistoryDto>> GetInternalAllEconomicSubstanceHistory(GetDetailByIDDto input)
        {
            var repo = GetRepository<EconomicSubstanceDeclarationHistory>();

            var result = await repo.Query(x => x.EconomicSubstanceDeclarationId == input.Id).OrderByDescending(x => x.CreatedAt).ToListAsync();
            return Mapper.Map<List<EconomicSubstanceDeclarationHistoryDto>>(result);

        }


        public async Task<EconomicSubstanceDeclarationDto> GetEconomicSubstanceDetailHistory(GetEconomicSubstanceByEntityIdDto input)
        {
            var repo = GetRepository<EconomicSubstanceDeclarationHistory>();
            var result = await repo.Query(x => x.Id == input.Id).FirstOrDefaultAsync();
            return result != null ? result.EconomicSubstanceDeclarationObject.ConvertCompressedJsonToObject<EconomicSubstanceDeclarationDto>() : new EconomicSubstanceDeclarationDto();
        }

        public async Task MigrateAllSubmittedAssessments(NoInput input)
        {
            var repo = GetRepository<EconomicSubstanceDeclaration>();
            var result = await repo.Query().Include(x => x.RelevantActivities).Where(x =>
                                                                               !x.IsDeleted &&
                                                                               (x.Status == EconomicSubstanceStatus.Submitted || x.Status == EconomicSubstanceStatus.ReSubmitted) &&
                                                                                 x.RelevantActivities.Any(y => y.ReleventActivityValue == RelevantActivity.IntelBA
                                                                                                              && y.IsChecked && !y.IsDeleted && (y.IsLegalEntityHighRisk ?? false))).ToListAsync();
            foreach (var item in result)
            {
                GetEconomicSubstanceByEntityDetailIdDto id = new GetEconomicSubstanceByEntityDetailIdDto() { Id = item.Id };
                var mappedItem = await GetEconomicSubstanceDetailById(id);
                await _workflowMicroservice.Call(x => x.MigrateSubmittedAssessments(mappedItem));
            }

        }


        public async Task<CaAdditionalStatisticsDto> GenerateAdditionalStatistics(CaDashboardInput caDashboardInput)
        {
            using (Logger.BeginScope(new Dictionary<string, object>(){
                {"CSP", caDashboardInput.CtspNumber },
                {"YearESS", caDashboardInput.Year }
            }))
            {
                try
                {
                    Logger.LogInformation($"Starting GenerateAdditionalStatistics Execution For {caDashboardInput.CtspNumber} and Year {caDashboardInput.Year}");
                    var context = GetDbContext();
                    var additionalStatistics = await AdditionalStatisticHelper.GetAdditionalStatistics(context, caDashboardInput.Year);
                    return additionalStatistics;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error in GenerateAdditionalStatistics: {CSP} {Year}", caDashboardInput.CtspNumber, caDashboardInput.Year);
                    throw;
                }
            }
        }


        public async Task<List<string>> GetListOfJurisdictionNames(CaDashboardInput caDashboardInput)
        {
            using (Logger.BeginScope(new Dictionary<string, object>(){
                {"CSP", caDashboardInput.CtspNumber },
                {"YearESS", caDashboardInput.Year }
            }))
            {
                try
                {
                    Logger.LogInformation($"Starting GetListOfJurisdictionNames Execution For {caDashboardInput.CtspNumber} and Year {caDashboardInput.Year}");
                    var context = GetDbContext();
                    var jurisdictionTaxResidentNames = await AdditionalStatisticHelper.GetListOfJurisdictionNames(context, caDashboardInput.Year);
                    return jurisdictionTaxResidentNames?.ToList();
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error in GetListOfJurisdictionNames: {CSP} {Year}", caDashboardInput.CtspNumber, caDashboardInput.Year);
                    throw;
                }
            }
        }        
    }
}
